{"MonoBehaviour": {"m_Enabled": true, "m_EditorHideFlags": 0, "m_Name": "BuilderDocument", "m_EditorClassIdentifier": "UnityEditor.UIBuilderModule:Unity.UI.Builder:BuilderDocument", "m_SavedBuilderUxmlToThemeStyleSheetList": [], "m_CurrentCanvasTheme": 4, "m_CurrentCanvasThemeStyleSheetReference": {"fileID": -4733365628477956816, "guid": "c22aceab510b0894294a27c30e882cbd", "type": 3}, "m_CodePreviewVisible": true, "m_OpenUXMLFiles": [{"m_OpenUSSFiles": [{"m_StyleSheet": {"fileID": 7433441132597879392, "guid": "5fd60a379518b4944a4057c8b14bf425", "type": 3}, "m_ContentHash": -1116885006, "m_UssPreview": ".settings-panel-root {\r\n    background-color: rgb(42, 42, 42);\r\n    flex-grow: 1;\r\n    padding: 20px;\r\n    border-width: 2px;\r\n    border-color: rgb(74, 74, 74);\r\n    justify-content: space-between;\r\n    align-items: stretch;\r\n}\r\n\r\n.panel-title {\r\n    font-size: 36px;\r\n    color: rgb(224, 224, 224);\r\n    -unity-font-style: bold;\r\n    -unity-text-align: middle-center;\r\n    margin-bottom: 20px;\r\n    padding-bottom: 10px;\r\n    border-bottom-width: 1px;\r\n    border-bottom-color: rgb(85, 85, 85);\r\n}\r\n\r\n.settings-scroll-view {\r\n    flex-grow: 1;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.settings-section {\r\n    background-color: rgb(58, 58, 58);\r\n    margin-bottom: 15px;\r\n    padding: 10px;\r\n    border-width: 1px;\r\n    border-color: rgb(85, 85, 85);\r\n    border-radius: 8px;\r\n}\r\n\r\n.settings-section > .unity-foldout__header {\r\n    background-color: rgb(74, 74, 74);\r\n    padding: 10px;\r\n    font-size: 24px;\r\n    color: rgb(240, 240, 240);\r\n    -unity-font-style: bold;\r\n    border-radius: 6px;\r\n}\r\n\r\n.settings-section > .unity-foldout__content {\r\n    padding: 10px 0 0 0;\r\n}\r\n\r\n.section-content {\r\n    flex-direction: column;\r\n    margin-top: 10px;\r\n}\r\n\r\n.setting-label {\r\n    font-size: 20px;\r\n    color: rgb(208, 208, 208);\r\n    margin-top: 10px;\r\n    margin-bottom: 5px;\r\n}\r\n\r\n.setting-slider {\r\n    height: 30px;\r\n    margin-bottom: 15px;\r\n}\r\n\r\n.setting-slider > .unity-base-slider__tracker {\r\n    background-color: rgb(85, 85, 85);\r\n    height: 8px;\r\n    border-radius: 4px;\r\n}\r\n\r\n.setting-slider > .unity-base-slider__dragger {\r\n    background-color: rgb(0, 123, 255);\r\n    width: 24px;\r\n    height: 24px;\r\n    border-radius: 12px;\r\n    border-width: 2px;\r\n    border-color: rgb(255, 255, 255);\r\n}\r\n\r\n.setting-dropdown {\r\n    height: 40px;\r\n    margin-bottom: 15px;\r\n    background-color: rgb(85, 85, 85);\r\n    color: rgb(224, 224, 224);\r\n    font-size: 20px;\r\n    border-width: 1px;\r\n    border-color: rgb(119, 119, 119);\r\n    border-radius: 5px;\r\n}\r\n\r\n.setting-toggle {\r\n    height: 30px;\r\n    margin-bottom: 15px;\r\n}\r\n\r\n.setting-toggle > .unity-toggle__input {\r\n    background-color: rgb(85, 85, 85);\r\n    border-width: 1px;\r\n    border-color: rgb(119, 119, 119);\r\n    border-radius: 15px;\r\n    width: 50px;\r\n    height: 30px;\r\n}\r\n\r\n.setting-toggle > .unity-toggle__input > .unity-toggle__checkmark {\r\n    background-color: rgb(0, 123, 255);\r\n    border-radius: 12px;\r\n    width: 26px;\r\n    height: 26px;\r\n    left: 2px;\r\n    top: 2px;\r\n}\r\n\r\n.setting-toggle > .unity-toggle__text {\r\n    color: rgb(208, 208, 208);\r\n    font-size: 20px;\r\n    margin-left: 10px;\r\n}\r\n\r\n.button-container {\r\n    flex-direction: row;\r\n    justify-content: space-around;\r\n    align-items: center;\r\n    margin-top: 20px;\r\n    padding-top: 15px;\r\n    border-top-width: 1px;\r\n    border-top-color: rgb(85, 85, 85);\r\n}\r\n\r\n.action-button {\r\n    width: 120px;\r\n    height: 50px;\r\n    font-size: 22px;\r\n    background-color: rgb(0, 123, 255);\r\n    color: rgb(255, 255, 255);\r\n    border-radius: 8px;\r\n    border-width: 0;\r\n    -unity-font-style: bold;\r\n}\r\n", "m_OldPath": "Assets/UI Toolkit/Generated/GeneratedUI_20250527_201052.uss"}], "m_OpenendVisualTreeAssetOldPath": "Assets/UI Toolkit/Generated/GeneratedUI_20250527_201052.uxml", "m_UxmlPreview": "<ui:UXML xmlns:ui=\"UnityEngine.UIElements\">\r\n    <Style src=\"project://database/Assets/UI%20Toolkit/Generated/GeneratedUI_20250527_201052.uss?fileID=7433441132597879392&amp;guid=5fd60a379518b4944a4057c8b14bf425&amp;type=3#GeneratedUI_20250527_201052\" />\r\n    <ui:VisualElement class=\"settings-panel-root\">\r\n        <ui:Label text=\"Settings\" class=\"panel-title\" />\r\n        <ui:ScrollView class=\"settings-scroll-view\">\r\n            <ui:Foldout text=\"Audio\" class=\"settings-section audio-section\">\r\n                <ui:VisualElement class=\"section-content\">\r\n                    <ui:Label text=\"Master Volume\" class=\"setting-label\" />\r\n                    <ui:Slider low-value=\"0\" high-value=\"100\" value=\"75\" class=\"setting-slider master-volume-slider\" />\r\n                    <ui:Label text=\"Music Volume\" class=\"setting-label\" />\r\n                    <ui:Slider low-value=\"0\" high-value=\"100\" value=\"60\" class=\"setting-slider music-volume-slider\" />\r\n                    <ui:Label text=\"SFX Volume\" class=\"setting-label\" />\r\n                    <ui:Slider low-value=\"0\" high-value=\"100\" value=\"80\" class=\"setting-slider sfx-volume-slider\" />\r\n                </ui:VisualElement>\r\n            </ui:Foldout>\r\n            <ui:Foldout text=\"Graphics\" class=\"settings-section graphics-section\">\r\n                <ui:VisualElement class=\"section-content\">\r\n                    <ui:Label text=\"Quality Preset\" class=\"setting-label\" />\r\n                    <ui:DropdownField class=\"setting-dropdown quality-dropdown\" />\r\n                    <ui:Toggle label=\"VSync\" value=\"true\" class=\"setting-toggle vsync-toggle\" />\r\n                    <ui:Toggle label=\"Post Processing\" value=\"true\" class=\"setting-toggle post-processing-toggle\" />\r\n                </ui:VisualElement>\r\n            </ui:Foldout>\r\n            <ui:Foldout text=\"Controls\" class=\"settings-section controls-section\">\r\n                <ui:VisualElement class=\"section-content\">\r\n                    <ui:Label text=\"Look Sensitivity\" class=\"setting-label\" />\r\n                    <ui:Slider low-value=\"1\" high-value=\"10\" value=\"5\" class=\"setting-slider sensitivity-slider\" />\r\n                    <ui:Toggle label=\"Invert Y-Axis\" value=\"false\" class=\"setting-toggle invert-y-toggle\" />\r\n                    <ui:Toggle label=\"Vibration Feedback\" value=\"true\" class=\"setting-toggle vibration-toggle\" />\r\n                </ui:VisualElement>\r\n            </ui:Foldout>\r\n        </ui:ScrollView>\r\n        <ui:VisualElement class=\"button-container\">\r\n            <ui:Button text=\"Apply\" class=\"action-button apply-button\" />\r\n            <ui:Button text=\"Back\" class=\"action-button back-button\" />\r\n        </ui:VisualElement>\r\n    </ui:VisualElement>\r\n</ui:UXML>\r\n", "m_ContentHash": -637913450, "m_VisualTreeAssetRef": {"fileID": 9197481963319205126, "guid": "eae048b9e6953044c8c6c3bf06a69990", "type": 3}, "m_ActiveStyleSheet": {"instanceID": 0}, "m_Settings": {"UxmlGuid": "eae048b9e6953044c8c6c3bf06a69990", "UxmlPath": "Assets/UI Toolkit/Generated/GeneratedUI_20250527_201052.uxml", "CanvasX": 0, "CanvasY": 0, "CanvasWidth": 350, "CanvasHeight": 450, "MatchGameView": false, "ZoomScale": 1.0, "PanOffset": {"x": 20.0, "y": 20.0}, "ColorModeBackgroundOpacity": 1.0, "ImageModeCanvasBackgroundOpacity": 1.0, "CameraModeCanvasBackgroundOpacity": 1.0, "EnableCanvasBackground": false, "CanvasBackgroundMode": 0, "CanvasBackgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}, "CanvasBackgroundImage": {"instanceID": 0}, "CanvasBackgroundImageScaleMode": 1, "CanvasBackgroundCameraName": ""}, "m_OpenSubDocumentParentIndex": -1, "m_OpenSubDocumentParentSourceTemplateAssetIndex": -1}], "m_ActiveOpenUXMLFileIndex": 0}}