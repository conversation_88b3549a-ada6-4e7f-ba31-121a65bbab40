{"MonoBehaviour": {"m_Enabled": true, "m_EditorHideFlags": 0, "m_Name": "BuilderDocument", "m_EditorClassIdentifier": "UnityEditor.UIBuilderModule:Unity.UI.Builder:BuilderDocument", "m_SavedBuilderUxmlToThemeStyleSheetList": [], "m_CurrentCanvasTheme": 4, "m_CurrentCanvasThemeStyleSheetReference": {"fileID": -4733365628477956816, "guid": "c22aceab510b0894294a27c30e882cbd", "type": 3}, "m_CodePreviewVisible": true, "m_OpenUXMLFiles": [{"m_OpenUSSFiles": [{"m_StyleSheet": {"fileID": 7433441132597879392, "guid": "77105d4a936069a4983ecde7d3aaaceb", "type": 3}, "m_ContentHash": -235703161, "m_UssPreview": ".dashboard-layout {\r\n    flex-grow: 1;\r\n    background-color: rgb(42, 42, 42);\r\n}\r\n\r\n.sidebar {\r\n    background-color: rgb(51, 51, 51);\r\n    width: 200px;\r\n    flex-direction: column;\r\n    padding: 20px;\r\n    border-right-width: 1px;\r\n    border-color: rgb(68, 68, 68);\r\n}\r\n\r\n.sidebar-title {\r\n    color: white;\r\n    font-size: 24px;\r\n    -unity-font-style: Bold;\r\n    margin-bottom: 30px;\r\n    -unity-text-align: middle-center;\r\n}\r\n\r\n.sidebar-navigation {\r\n    flex-direction: column;\r\n    flex-grow: 1;\r\n}\r\n\r\n.sidebar-button {\r\n    background-color: rgb(68, 68, 68);\r\n    color: white;\r\n    font-size: 16px;\r\n    padding: 10px 15px;\r\n    margin-bottom: 10px;\r\n    border-radius: 5px;\r\n    border-width: 0;\r\n    -unity-text-align: middle-left;\r\n    flex-grow: 0;\r\n    width: auto;\r\n    height: auto;\r\n}\r\n\r\n.dashboard-content-scrollview {\r\n    flex-grow: 1;\r\n}\r\n\r\n.dashboard-content {\r\n    flex-direction: column;\r\n    padding: 30px;\r\n    flex-grow: 1;\r\n}\r\n\r\n.dashboard-title {\r\n    color: white;\r\n    font-size: 32px;\r\n    -unity-font-style: Bold;\r\n    margin-bottom: 30px;\r\n}\r\n\r\n.section-title {\r\n    color: white;\r\n    font-size: 24px;\r\n    -unity-font-style: Bold;\r\n    margin-top: 40px;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.card-container {\r\n    flex-direction: row;\r\n    flex-wrap: wrap;\r\n    justify-content: space-between;\r\n    margin-bottom: 30px;\r\n}\r\n\r\n.statistic-card {\r\n    background-color: rgb(51, 51, 51);\r\n    border-width: 1px;\r\n    border-color: rgb(68, 68, 68);\r\n    border-radius: 8px;\r\n    padding: 20px;\r\n    margin: 10px;\r\n    width: 280px;\r\n    min-height: 200px;\r\n    flex-direction: column;\r\n    flex-grow: 0;\r\n}\r\n\r\n.card-header {\r\n    color: rgb(204, 204, 204);\r\n    font-size: 18px;\r\n    -unity-font-style: Bold;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.card-value {\r\n    color: white;\r\n    font-size: 36px;\r\n    -unity-font-style: Bold;\r\n    margin-bottom: 15px;\r\n}\r\n\r\n.chart-placeholder {\r\n    background-color: rgb(74, 74, 74);\r\n    height: 80px;\r\n    border-radius: 4px;\r\n    margin-top: 10px;\r\n}\r\n\r\n.metrics-section {\r\n    flex-direction: column;\r\n    width: 600px;\r\n}\r\n\r\n.metric-item {\r\n    flex-direction: row;\r\n    align-items: center;\r\n    margin-bottom: 15px;\r\n    padding: 10px;\r\n    background-color: rgb(56, 56, 56);\r\n    border-radius: 5px;\r\n}\r\n\r\n.metric-label {\r\n    color: white;\r\n    font-size: 16px;\r\n    flex-grow: 1;\r\n    margin-right: 20px;\r\n}\r\n\r\n.metric-progress-bar {\r\n    width: 250px;\r\n    height: 20px;\r\n    background-color: rgb(74, 74, 74);\r\n    border-radius: 10px;\r\n}\r\n\r\n.metric-progress-bar #unity-progress-bar__progress {\r\n    background-color: rgb(0, 123, 255);\r\n    border-radius: 10px;\r\n}\r\n", "m_OldPath": "Assets/UI Toolkit/Generated/GeneratedUI_20250527_200505.uss"}], "m_OpenendVisualTreeAssetOldPath": "Assets/UI Toolkit/Generated/GeneratedUI_20250527_200505.uxml", "m_UxmlPreview": "<ui:UXML xmlns:ui=\"UnityEngine.UIElements\">\r\n    <Style src=\"project://database/Assets/UI%20Toolkit/Generated/GeneratedUI_20250527_200505.uss?fileID=7433441132597879392&amp;guid=77105d4a936069a4983ecde7d3aaaceb&amp;type=3#GeneratedUI_20250527_200505\" />\r\n    <ui:TwoPaneSplitView fixed-pane-initial-dimension=\"200\" orientation=\"Horizontal\" class=\"dashboard-layout\">\r\n        <ui:VisualElement class=\"sidebar\">\r\n            <ui:Label text=\"Dashboard\" class=\"sidebar-title\" />\r\n            <ui:VisualElement class=\"sidebar-navigation\">\r\n                <ui:Button text=\"Overview\" class=\"sidebar-button\" />\r\n                <ui:Button text=\"Analytics\" class=\"sidebar-button\" />\r\n                <ui:Button text=\"Reports\" class=\"sidebar-button\" />\r\n                <ui:Button text=\"Settings\" class=\"sidebar-button\" />\r\n            </ui:VisualElement>\r\n        </ui:VisualElement>\r\n        <ui:ScrollView class=\"dashboard-content-scrollview\">\r\n            <ui:VisualElement class=\"dashboard-content\">\r\n                <ui:Label text=\"Dashboard Overview\" class=\"dashboard-title\" />\r\n                <ui:VisualElement class=\"card-container\">\r\n                    <ui:Box class=\"statistic-card\">\r\n                        <ui:Label text=\"Total Sales\" class=\"card-header\" />\r\n                        <ui:Label text=\"$12,345\" class=\"card-value\" />\r\n                        <ui:VisualElement class=\"chart-placeholder\" />\r\n                    </ui:Box>\r\n                    <ui:Box class=\"statistic-card\">\r\n                        <ui:Label text=\"New Users\" class=\"card-header\" />\r\n                        <ui:Label text=\"1,234\" class=\"card-value\" />\r\n                        <ui:VisualElement class=\"chart-placeholder\" />\r\n                    </ui:Box>\r\n                    <ui:Box class=\"statistic-card\">\r\n                        <ui:Label text=\"Website Traffic\" class=\"card-header\" />\r\n                        <ui:Label text=\"56,789\" class=\"card-value\" />\r\n                        <ui:VisualElement class=\"chart-placeholder\" />\r\n                    </ui:Box>\r\n                    <ui:Box class=\"statistic-card\">\r\n                        <ui:Label text=\"Conversion Rate\" class=\"card-header\" />\r\n                        <ui:Label text=\"4.5%\" class=\"card-value\" />\r\n                        <ui:VisualElement class=\"chart-placeholder\" />\r\n                    </ui:Box>\r\n                </ui:VisualElement>\r\n                <ui:Label text=\"Key Metrics\" class=\"section-title\" />\r\n                <ui:VisualElement class=\"metrics-section\">\r\n                    <ui:VisualElement class=\"metric-item\">\r\n                        <ui:Label text=\"Project Progress\" class=\"metric-label\" />\r\n                        <ui:ProgressBar low-value=\"0\" high-value=\"100\" value=\"75\" class=\"metric-progress-bar\" />\r\n                    </ui:VisualElement>\r\n                    <ui:VisualElement class=\"metric-item\">\r\n                        <ui:Label text=\"Task Completion\" class=\"metric-label\" />\r\n                        <ui:ProgressBar low-value=\"0\" high-value=\"100\" value=\"90\" class=\"metric-progress-bar\" />\r\n                    </ui:VisualElement>\r\n                    <ui:VisualElement class=\"metric-item\">\r\n                        <ui:Label text=\"Resource Utilization\" class=\"metric-label\" />\r\n                        <ui:ProgressBar low-value=\"0\" high-value=\"100\" value=\"60\" class=\"metric-progress-bar\" />\r\n                    </ui:VisualElement>\r\n                </ui:VisualElement>\r\n            </ui:VisualElement>\r\n        </ui:ScrollView>\r\n    </ui:TwoPaneSplitView>\r\n</ui:UXML>\r\n", "m_ContentHash": -16294896, "m_VisualTreeAssetRef": {"fileID": 9197481963319205126, "guid": "c19cc4a9a5cf01c4f857158f9848cffe", "type": 3}, "m_ActiveStyleSheet": {"instanceID": 0}, "m_Settings": {"UxmlGuid": "c19cc4a9a5cf01c4f857158f9848cffe", "UxmlPath": "Assets/UI Toolkit/Generated/GeneratedUI_20250527_200505.uxml", "CanvasX": 0, "CanvasY": 0, "CanvasWidth": 350, "CanvasHeight": 450, "MatchGameView": false, "ZoomScale": 1.0, "PanOffset": {"x": 20.0, "y": 20.0}, "ColorModeBackgroundOpacity": 1.0, "ImageModeCanvasBackgroundOpacity": 1.0, "CameraModeCanvasBackgroundOpacity": 1.0, "EnableCanvasBackground": false, "CanvasBackgroundMode": 0, "CanvasBackgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}, "CanvasBackgroundImage": {"instanceID": 0}, "CanvasBackgroundImageScaleMode": 1, "CanvasBackgroundCameraName": ""}, "m_OpenSubDocumentParentIndex": -1, "m_OpenSubDocumentParentSourceTemplateAssetIndex": -1}], "m_ActiveOpenUXMLFileIndex": 0}}