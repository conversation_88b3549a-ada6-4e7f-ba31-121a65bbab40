{"MonoBehaviour": {"m_Enabled": true, "m_EditorHideFlags": 0, "m_Name": "BuilderDocument", "m_EditorClassIdentifier": "UnityEditor.UIBuilderModule:Unity.UI.Builder:BuilderDocument", "m_SavedBuilderUxmlToThemeStyleSheetList": [], "m_CurrentCanvasTheme": 4, "m_CurrentCanvasThemeStyleSheetReference": {"fileID": -*******************, "guid": "c22aceab510b0894294a27c30e882cbd", "type": 3}, "m_CodePreviewVisible": true, "m_OpenUXMLFiles": [{"m_OpenUSSFiles": [{"m_StyleSheet": {"fileID": 7433441132597879392, "guid": "1ecdaeec731ec7d47b86e6ceac1c88b6", "type": 3}, "m_ContentHash": 10098425, "m_UssPreview": ".LoginForm {\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    min-height: 100%;\r\n}\r\n\r\n.Card {\r\n    background-color: white;\r\n    border-radius: 8px;\r\n    padding: 20px;\r\n    width: 300px;\r\n}\r\n\r\n.Title {\r\n    font-size: 24px;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.TextField {\r\n    width: 100%;\r\n    margin-bottom: 10px;\r\n    padding: 10px;\r\n    border: 1px solid rgb(204, 204, 204);\r\n    border-radius: 4px;\r\n    font-size: 16px;\r\n}\r\n\r\n.LoginButton {\r\n    width: 100%;\r\n    margin-top: 10px;\r\n    padding: 10px;\r\n    border-radius: 4px;\r\n    font-size: 16px;\r\n    color: white;\r\n}\r\n\r\n.ForgotPasswordLink {\r\n    margin-top: 10px;\r\n    -unity-text-align: center;\r\n    color: rgb(0, 123, 255);\r\n    text-decoration: underline;\r\n    font-size: 14px;\r\n}\r\n", "m_OldPath": "Assets/UI Toolkit/Generated/GeneratedUI_20250527_193529.uss"}], "m_OpenendVisualTreeAssetOldPath": "Assets/UI Toolkit/Generated/GeneratedUI_20250527_193529.uxml", "m_UxmlPreview": "<ui:UXML xmlns:ui=\"UnityEngine.UIElements\">\r\n    <Style src=\"project://database/Assets/UI%20Toolkit/Generated/GeneratedUI_20250527_193529.uss?fileID=7433441132597879392&amp;guid=1ecdaeec731ec7d47b86e6ceac1c88b6&amp;type=3#GeneratedUI_20250527_193529\" />\r\n    <ui:VisualElement class=\"LoginForm\">\r\n        <ui:Panel class=\"Card\">\r\n            <ui:Label text=\"Login\" class=\"Title\" />\r\n            <ui:TextField id=\"UsernameField\" placeholder=\"Username\" class=\"TextField\" />\r\n            <ui:TextField id=\"PasswordField\" placeholder=\"Password\" isPasswordField=\"true\" class=\"TextField\" />\r\n            <ui:Button text=\"Login\" class=\"LoginButton\" />\r\n            <ui:Label text=\"Forgot Password?\" class=\"ForgotPasswordLink\" />\r\n        </ui:Panel>\r\n    </ui:VisualElement>\r\n</ui:UXML>\r\n", "m_ContentHash": 485164126, "m_VisualTreeAssetRef": {"fileID": 9197481963319205126, "guid": "d498b4b7152540440ac10f212d390ef5", "type": 3}, "m_ActiveStyleSheet": {"instanceID": 0}, "m_Settings": {"UxmlGuid": "d498b4b7152540440ac10f212d390ef5", "UxmlPath": "Assets/UI Toolkit/Generated/GeneratedUI_20250527_193529.uxml", "CanvasX": 0, "CanvasY": 0, "CanvasWidth": 350, "CanvasHeight": 450, "MatchGameView": false, "ZoomScale": 1.0, "PanOffset": {"x": 20.0, "y": 20.0}, "ColorModeBackgroundOpacity": 1.0, "ImageModeCanvasBackgroundOpacity": 1.0, "CameraModeCanvasBackgroundOpacity": 1.0, "EnableCanvasBackground": false, "CanvasBackgroundMode": 0, "CanvasBackgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}, "CanvasBackgroundImage": {"instanceID": 0}, "CanvasBackgroundImageScaleMode": 1, "CanvasBackgroundCameraName": ""}, "m_OpenSubDocumentParentIndex": -1, "m_OpenSubDocumentParentSourceTemplateAssetIndex": -1}], "m_ActiveOpenUXMLFileIndex": 0}}