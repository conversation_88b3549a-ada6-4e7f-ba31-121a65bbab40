%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d2ef99971794c4b4f825156f5736ca99, type: 3}
  m_Name: GeminiUIGeneratorSettings
  m_EditorClassIdentifier: Assembly-CSharp-Editor::AIStaff.UIGenerator.GeminiUIGeneratorSettings
  _apiKey: 
  _selectedModel: gemini-1.5-pro
  _requestTimeout: 60
  _maxRetries: 3
  _lastPrompt: 
  _promptHistory: []
  _generationPreferences:
    autoApplyGenerated: 0
    showLivePreview: 1
    enableSyntaxHighlighting: 1
    saveGenerationHistory: 1
    autoSavePrompts: 1
    maxHistoryEntries: 50
    defaultOutputFolder: Assets/UI Toolkit/Generated
    useCustomNaming: 0
    customNamingPattern: GeneratedUI_{timestamp}
  _editorPreferences:
    compactMode: 0
    windowOpacity: 1
    showTooltips: 1
    enableAnimations: 1
    themePreference: Auto
    showAdvancedOptions: 0
    lastWindowSize: {x: 800, y: 600}
    lastWindowPosition: {x: 100, y: 100}
  _usageStats:
    totalRequests: 0
    successfulRequests: 0
    failedRequests: 0
    averageResponseTime: 0
  _enableDebugLogging: 0
  _enableTelemetry: 1
  _customAPIEndpoint: 
