%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d2ef99971794c4b4f825156f5736ca99, type: 3}
  m_Name: GeminiUIGeneratorSettings
  m_EditorClassIdentifier: Assembly-CSharp-Editor::AIStaff.UIGenerator.GeminiUIGeneratorSettings
  _apiKey: AIzaSyA6w8oNbh3rZZCYdQMBMItmCb_HOV3diR8
  _selectedModel: gemini-2.5-flash-preview-05-20
  _requestTimeout: 60
  _maxRetries: 3
  _lastPrompt: Create a login form with username and password text fields, a login
    button, and a 'Forgot Password?' link. Use a modern card-style layout with rounded
    corners and subtle shadows. Include proper spacing and hover effects.
  _promptHistory:
  - Create a login form with username and password text fields, a login button, and
    a 'Forgot Password?' link. Use a modern card-style layout with rounded corners
    and subtle shadows. Include proper spacing and hover effects.
  - "Create a settings panel with sections for Audio, Graphics, and Controls. Include
    sliders for volume controls, dropdown menus for quality settings, and toggle
    switches for various options. Use a clean, organized layout with clear section
    headers. Mobile games i\xE7in"
  - "Create a settings panel with sections for Audio, Graphics, and Controls. Include
    sliders for volume controls, dropdown menus for quality settings, and toggle
    switches for various options. Use a clean, organized layout with clear section
    headers. Mobile games i\xE7i"
  - "Create a settings panel with sections for Audio, Graphics, and Controls. Include
    sliders for volume controls, dropdown menus for quality settings, and toggle
    switches for various options. Use a clean, organized layout with clear section
    headers. Mobile games i\xE7"
  - Create a settings panel with sections for Audio, Graphics, and Controls. Include
    sliders for volume controls, dropdown menus for quality settings, and toggle
    switches for various options. Use a clean, organized layout with clear section
    headers. Mobile games i
  - 'Create a settings panel with sections for Audio, Graphics, and Controls. Include
    sliders for volume controls, dropdown menus for quality settings, and toggle
    switches for various options. Use a clean, organized layout with clear section
    headers. Mobile games '
  - Create a settings panel with sections for Audio, Graphics, and Controls. Include
    sliders for volume controls, dropdown menus for quality settings, and toggle
    switches for various options. Use a clean, organized layout with clear section
    headers. Mobile games
  - Create a settings panel with sections for Audio, Graphics, and Controls. Include
    sliders for volume controls, dropdown menus for quality settings, and toggle
    switches for various options. Use a clean, organized layout with clear section
    headers. Mobile game
  - Create a settings panel with sections for Audio, Graphics, and Controls. Include
    sliders for volume controls, dropdown menus for quality settings, and toggle
    switches for various options. Use a clean, organized layout with clear section
    headers. Mobile gam
  - Create a settings panel with sections for Audio, Graphics, and Controls. Include
    sliders for volume controls, dropdown menus for quality settings, and toggle
    switches for various options. Use a clean, organized layout with clear section
    headers. Mobile ga
  - Create a settings panel with sections for Audio, Graphics, and Controls. Include
    sliders for volume controls, dropdown menus for quality settings, and toggle
    switches for various options. Use a clean, organized layout with clear section
    headers. Mobile g
  - 'Create a settings panel with sections for Audio, Graphics, and Controls. Include
    sliders for volume controls, dropdown menus for quality settings, and toggle
    switches for various options. Use a clean, organized layout with clear section
    headers. Mobile '
  - Create a settings panel with sections for Audio, Graphics, and Controls. Include
    sliders for volume controls, dropdown menus for quality settings, and toggle
    switches for various options. Use a clean, organized layout with clear section
    headers. Mobile
  - Create a settings panel with sections for Audio, Graphics, and Controls. Include
    sliders for volume controls, dropdown menus for quality settings, and toggle
    switches for various options. Use a clean, organized layout with clear section
    headers. Mobil
  - Create a settings panel with sections for Audio, Graphics, and Controls. Include
    sliders for volume controls, dropdown menus for quality settings, and toggle
    switches for various options. Use a clean, organized layout with clear section
    headers. Mobi
  - Create a settings panel with sections for Audio, Graphics, and Controls. Include
    sliders for volume controls, dropdown menus for quality settings, and toggle
    switches for various options. Use a clean, organized layout with clear section
    headers. Mob
  - Create a settings panel with sections for Audio, Graphics, and Controls. Include
    sliders for volume controls, dropdown menus for quality settings, and toggle
    switches for various options. Use a clean, organized layout with clear section
    headers. Mo
  - Create a settings panel with sections for Audio, Graphics, and Controls. Include
    sliders for volume controls, dropdown menus for quality settings, and toggle
    switches for various options. Use a clean, organized layout with clear section
    headers. M
  - 'Create a settings panel with sections for Audio, Graphics, and Controls. Include
    sliders for volume controls, dropdown menus for quality settings, and toggle
    switches for various options. Use a clean, organized layout with clear section
    headers. '
  - Create a settings panel with sections for Audio, Graphics, and Controls. Include
    sliders for volume controls, dropdown menus for quality settings, and toggle
    switches for various options. Use a clean, organized layout with clear section
    headers.
  - "Mobil bir oyun i\xE7in, oyun i\xE7i (Ingame) men\xFC i\xE7in kapsaml\u0131 bir
    aray\xFCz layout tasar\u0131m\u0131 yapal\u0131m. Bu men\xFC, oyun s\u0131ras\u0131nda
    eri\u015Filebilen bir Duraklatma/Ayarlar men\xFCs\xFC olarak i\u015Flev g\xF6recektir.
    Tasar\u0131m, Unity UI Toolkit'in yeteneklerini kullanarak mobil cihazlarda hem
    estetik hem de kullan\u0131c\u0131 dostu olacak \u015Fekilde detayland\u0131r\u0131lmal\u0131d\u0131r.\n\n**Genel
    Yap\u0131 ve Konumland\u0131rma:**\n*   **Ana Konteyner (`VisualElement`):**
    `root-menu-container` ad\u0131nda tam ekran kaplayan bir `VisualElement` kullan\u0131ls\u0131n.\n   
    *   Bu element, oyun ekran\u0131n\u0131n \xFCzerine bindirilecek ve hafif\xE7e
    karart\u0131lm\u0131\u015F bir arka plana sahip olacak (`background-color: rgba(0,
    0, 0, 0.7)`).\n    *   `display: flex`, `flex-direction: column`, `justify-content:
    center`, `align-items: center` \xF6zellikleri ile i\xE7erik dikey ve yatay olarak
    ortalanmal\u0131d\u0131r.\n    *   Arka plan iste\u011Fe ba\u011Fl\u0131 olarak
    hafif bir bulan\u0131kl\u0131k efekti i\xE7in `backdrop-filter: blur(5px)` (e\u011Fer
    Toolkit desteklerse) veya `Image` ile sim\xFCle edilmi\u015F bir bulan\u0131k
    texture kullanabilir.\n\n*   **Men\xFC Paneli (`VisualElement`):** `menu-panel`
    ad\u0131nda, ana konteyner i\xE7inde ortalanm\u0131\u015F bir ana men\xFC paneli.\n   
    *   Boyutland\u0131rma: Mobil cihazlar i\xE7in `width: 75%` ve `height: 85%`
    gibi y\xFCzde tabanl\u0131 de\u011Ferler, b\xFCy\xFCk ekranlar i\xE7in `max-width:
    600px` ve `max-height: 750px` ile s\u0131n\u0131rland\u0131r\u0131ls\u0131n.\n   
    *   Arka Plan: Koyu, \u015F\u0131k bir arka plan rengi (`background-color: #2C3E50`
    - koyu mavi-gri).\n    *   Kenarl\u0131k: `border-radius: 15px` ile yuvarlak
    k\xF6\u015Feler ve `border-width: 3px`, `border-color: #4CAF50` (canl\u0131 ye\u015Fil)
    ile belirgin bir d\u0131\u015F \xE7er\xE7eve.\n    *   G\xF6lge: `box-shadow`
    \xF6zelli\u011Fi ile hafif bir derinlik hissi verilsin (`box-shadow: 0px 8px
    16px rgba(0, 0, 0, 0.5)`).\n    *   Layout: \u0130\xE7erik i\xE7in `display:
    flex`, `flex-direction: column`, `align-items: stretch` ve `padding: 30px` kullan\u0131lmal\u0131.\n\n**\u0130\xE7erik
    Detaylar\u0131 ve Element \xD6nerileri:**\n\n1.  **Men\xFC Ba\u015Fl\u0131\u011F\u0131:**\n   
    *   **Element:** `Label` (`menu-title`).\n    *   **Metin:** \"PAUSE MENU\" veya
    \"DURAKLATMA MEN\xDCS\xDC\".\n    *   **Stil:** `font-size: 42px`, `color: #ECF0F1`
    (a\xE7\u0131k gri), `-unity-font-definition: resource(\"Fonts/Roboto-Bold\")`
    (kal\u0131n bir font), `margin-bottom: 40px`, `-unity-text-align: middle-center`.
    Ba\u015Fl\u0131\u011Fa hafif bir `text-shadow` eklenebilir.\n    *   **G\xF6rsel
    Hiyerar\u015Fi:** Men\xFCn\xFCn en belirgin \xF6\u011Fesi olmal\u0131.\n\n2. 
    **Ana Men\xFC Butonlar\u0131:**\n    *   **Konteyner (`VisualElement`):** `main-menu-buttons`
    ad\u0131nda, butonlar\u0131 dikey olarak d\xFCzenleyecek bir `VisualElement`.\n       
    *   `display: flex`, `flex-direction: column`, `justify-content: center` (dikey
    ortalama), `align-items: stretch` (yatayda geni\u015Fletme), `flex-grow: 1` (bo\u015F
    alan\u0131 doldurma), `gap: 20px` (butonlar aras\u0131 bo\u015Fluk).\n       
    *   `width: 80%` (men\xFC paneli i\xE7inde), `max-width: 400px`.\n    *   **Butonlar
    (`Button`):** S\u0131ras\u0131yla `resume-button` (\"DEVAM ET\"), `options-button`
    (\"AYARLAR\"), `restart-button` (\"YEN\u0130DEN BA\u015ELAT\"), `quit-button`
    (\"ANA MEN\xDCYE D\xD6N\") ad\u0131nda 4 adet buton.\n        *   **Ortak Stil
    (`USS Class: .menu-button`):**\n            *   `background-color: #3498DB` (parlak
    mavi).\n            *   `color: #FFFFFF`.\n            *   `font-size: 28px`
    (dokunmatik cihazlar i\xE7in geni\u015F hedef alan).\n            *   `-unity-font-definition:
    resource(\"Fonts/Roboto-Regular\")`.\n            *   `padding: 15px 25px`.\n           
    *   `border-radius: 10px`.\n            *   `border-width: 2px`, `border-color:
    #2980B9` (daha koyu mavi kenarl\u0131k).\n            *   `-unity-text-align:
    middle-center`.\n            *   **Etkile\u015Fim:** `transition-property: background-color,
    border-color`, `transition-duration: 0.2s`, `transition-timing-function: ease-out`
    ile yumu\u015Fak ge\xE7i\u015Fler.\n            *   `:hover` durumu: `background-color:
    #2980B9`, `border-color: #3498DB`, `cursor: pointer`.\n            *   `:active`
    durumu: `background-color: #21618C`, `border-color: #1A5276`.\n\n3.  **Ayarlar
    Paneli (Options Panel):**\n    *   **Konteyner (`VisualElement`):** `options-panel`
    ad\u0131nda, ba\u015Flang\u0131\xE7ta `display: none` olacak bir `VisualElement`.
    \"AYARLAR\" butonuna bas\u0131ld\u0131\u011F\u0131nda `display: flex` olarak
    de\u011Fi\u015Ftirilmeli ve ana men\xFC butonlar\u0131 gizlenmelidir.\n       
    *   `width: 100%`, `height: 100%` (ana panelin alan\u0131n\u0131 doldurmal\u0131).\n       
    *   `padding: 20px`.\n        *   `flex-direction: column`, `align-items: stretch`,
    `justify-content: flex-start`, `gap: 20px`.\n    *   **Ba\u015Fl\u0131k (`Label`):**
    `options-title` (`subheader-label` USS s\u0131n\u0131f\u0131 ile).\n        *  
    Metin: \"SETTINGS\" veya \"AYARLAR\".\n        *   Stil: `font-size: 34px`, `color:
    #BDC3C7`, `-unity-font-definition: resource(\"Fonts/Roboto-Medium\")`, `margin-bottom:
    30px`.\n    *   **Ayarlar Sat\u0131rlar\u0131 (`VisualElement`):** Her bir ayar
    i\xE7in `setting-row` s\u0131n\u0131f\u0131na sahip bir `VisualElement` kullan\u0131lmal\u0131.\n       
    *   `display: flex`, `flex-direction: row`, `justify-content: space-between`,
    `align-items: center`.\n        *   `padding-bottom: 10px`, `border-bottom-width:
    1px`, `border-bottom-color: #34495E` (ince ay\u0131r\u0131c\u0131 \xE7izgi).\n   
    *   **Ayar Etiketi (`Label`):** `setting-label` s\u0131n\u0131f\u0131 ile ayar\u0131n
    ad\u0131n\u0131 g\xF6steren `Label`.\n        *   Stil: `font-size: 22px`, `color:
    #ECF0F1`, `-unity-font-definition: resource(\"Fonts/Roboto-Light\")`, `flex-grow:
    1`, `margin-right: 20px`.\n\n    *   **Ses Ayarlar\u0131 (`Slider`):**\n       
    *   M\xFCzik Sesi: `music-volume-slider` (`audio-slider` s\u0131n\u0131f\u0131
    ile).\n            *   `Label` metni: \"MUSIC VOLUME\".\n            *   `low-value:
    0`, `high-value: 100`.\n        *   Efekt Sesi: `sfx-volume-slider` (`audio-slider`
    s\u0131n\u0131f\u0131 ile).\n            *   `Label` metni: \"SFX VOLUME\".\n           
    *   `low-value: 0`, `high-value: 100`.\n        *   **Slider Stil (`.audio-slider`):**\n           
    *   `width: 55%` (ayar sat\u0131r\u0131 i\xE7inde).\n            *   `height:
    25px` (dokunmatik kolayl\u0131\u011F\u0131 i\xE7in).\n            *   `-unity-slider-background-color:
    #7F8C8D` (track rengi).\n            *   `-unity-slider-dragger-color: #2ECC71`
    (ye\u015Fil dragger).\n            *   `border-radius: 5px`.\n\n    *   **FPS
    G\xF6stergesi (`Toggle`):**\n        *   `show-fps-toggle` (`toggle-style` s\u0131n\u0131f\u0131
    ile).\n        *   `Label` metni: \"SHOW FPS\".\n        *   **Toggle Stil (`.toggle-style`):**\n           
    *   `font-size: 22px`, `color: #ECF0F1`.\n            *   `-unity-toggle-checkmark-color:
    #2ECC71`.\n            *   `-unity-toggle-border-color: #7F8C8D`.\n           
    *   `-unity-toggle-background-color: #34495E`.\n\n    *   **Geri Butonu (`Button`):**
    `back-button` ad\u0131nda, ayarlar panelinden ana men\xFCye d\xF6nmek i\xE7in.\n       
    *   Metin: \"BACK\" veya \"GER\u0130\".\n        *   Stil: `background-color:
    #E74C3C` (k\u0131rm\u0131z\u0131), `color: #FFFFFF`, `font-size: 24px`, `padding:
    12px 20px`, `border-radius: 8px`, `border-width: 2px`, `border-color: #C0392B`.\n       
    *   `margin-top: 30px` ile ayarlar\u0131n alt\u0131ndan ayr\u0131lmal\u0131.\n       
    *   **Etkile\u015Fim:** `:hover` durumu: `background-color: #C0392B`, `border-color:
    #E74C3C`, `cursor: pointer`.\n\n**Etkile\u015Fim ve Kullan\u0131labilirlik Notlar\u0131:**\n*  
    **Dokunmatik Hedefler:** T\xFCm butonlar ve etkile\u015Fimli elementler (slider,
    toggle) mobil cihazlarda rahat\xE7a dokunulabilir boyutta olmal\u0131d\u0131r
    (minimum 48x48dp).\n*   **Geri Bildirim:** Butonlara bas\u0131ld\u0131\u011F\u0131nda
    hafif bir scale animasyonu veya renk de\u011Fi\u015Fimi gibi g\xF6rsel geri bildirimler
    (`transition`) sa\u011Flanmal\u0131.\n*   **Navigasyon:** \"AYARLAR\" butonuna
    bas\u0131ld\u0131\u011F\u0131nda `main-menu-buttons` `display: none` olmal\u0131
    ve `options-panel` `display: flex` olmal\u0131. \"GER\u0130\" butonuna bas\u0131ld\u0131\u011F\u0131nda
    bu durum tersine \xE7evrilmeli. Bu mant\u0131k C# kodu ile y\xF6netilmelidir.\n*  
    **Eri\u015Filebilirlik:** Metinlerin kontrast oran\u0131 y\xFCksek olmal\u0131
    ve okunabilir fontlar kullan\u0131lmal\u0131.\n*   **Performans:** A\u015F\u0131r\u0131
    karma\u015F\u0131k USS kurallar\u0131ndan ka\xE7\u0131n\u0131lmal\u0131, gereksiz
    yeniden \xE7izimleri \xF6nlemek i\xE7in `VisualElement` hiyerar\u015Fisi optimize
    edilmeli.\n\nBu detayl\u0131 tasar\u0131m, Unity UI Toolkit ile geli\u015Ftirme
    s\xFCrecinde do\u011Frudan kullan\u0131labilecek, hem g\xF6rsel hem de i\u015Flevsel
    bir temel sunmaktad\u0131r."
  - "Mobil bir oyun i\xE7in Ingame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131
    yapal\u0131m."
  - "Mobil bir oyun i\xE7inIngame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131
    yapal\u0131m."
  - "Mobil bir oyun i\xE7inmIngame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131
    yapal\u0131m."
  - "Mobil bir oyun i\xE7inm Ingame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131
    yapal\u0131m."
  - "Mobil bir oyun i\xE7iIngame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131
    yapal\u0131m."
  - "Mobil bir oyun i\xE7Ingame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131
    yapal\u0131m."
  - "Mobil bir oyun iIngame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yapal\u0131m."
  - "Mobil bir oyun Ingame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yapal\u0131m."
  - "Mobil bir oyunIngame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yapal\u0131m."
  - "Mobil bir oyuIngame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yapal\u0131m."
  - "Mobil bir oyIngame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yapal\u0131m."
  - "Mobil bir oIngame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yapal\u0131m."
  - "Mobil bir Ingame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yapal\u0131m."
  - "Mobil birIngame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yapal\u0131m."
  - "Mobil biIngame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yapal\u0131m."
  - "Mobil bIngame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yapal\u0131m."
  - "Mobil Ingame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yapal\u0131m."
  - "MobilIngame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yapal\u0131m."
  - "MobiIngame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yapal\u0131m."
  - "MobIngame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yapal\u0131m."
  - "MoIngame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yapal\u0131m."
  - "MIngame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yapal\u0131m."
  - "Ingame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yapal\u0131m."
  - "Ingame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yapal\u0131m"
  - "Ingame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yapal\u0131"
  - "Ingame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yapal"
  - "Ingame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yapa"
  - "Ingame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 yap"
  - "Ingame men\xFC i\xE7in aray\xFCz layout tasar\u0131m\u0131 ya"
  _generationPreferences:
    autoApplyGenerated: 0
    showLivePreview: 1
    enableSyntaxHighlighting: 1
    saveGenerationHistory: 1
    autoSavePrompts: 1
    maxHistoryEntries: 50
    defaultOutputFolder: Assets/UI Toolkit/Generated
    useCustomNaming: 0
    customNamingPattern: GeneratedUI_{timestamp}
  _editorPreferences:
    compactMode: 0
    windowOpacity: 1
    showTooltips: 1
    enableAnimations: 1
    themePreference: Auto
    showAdvancedOptions: 0
    lastWindowSize: {x: 800, y: 600}
    lastWindowPosition: {x: 100, y: 100}
  _usageStats:
    totalRequests: 0
    successfulRequests: 0
    failedRequests: 0
    averageResponseTime: 0
  _enableDebugLogging: 0
  _enableTelemetry: 1
  _customAPIEndpoint: 
