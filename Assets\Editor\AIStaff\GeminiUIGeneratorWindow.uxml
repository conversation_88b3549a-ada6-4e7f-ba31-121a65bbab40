<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements"
         xsi="http://www.w3.org/2001/XMLSchema-instance"
         engine="UnityEngine.UIElements"
         editor="UnityEditor.UIElements"
         noNamespaceSchemaLocation="../../../UIElementsSchema/UIElements.xsd"
         editor-extension-mode="True">

    <Style src="project://database/Assets/Editor/AIStaff/GeminiUIGeneratorWindow.uss" />

    <ui:VisualElement name="RootContainer" class="root-container">

        <!-- Header Section -->
        <ui:VisualElement name="HeaderSection" class="header-section">
            <ui:Label text="Gemini UI Generator" class="header-title" />
            <ui:Label name="HeaderSubtitle" text="Generate Unity UI Toolkit interfaces using AI" class="header-subtitle" />
        </ui:VisualElement>

        <!-- Settings Section -->
        <ui:VisualElement name="SettingsSection" class="settings-section">
            <ui:Label text="API Configuration" class="section-title" />

            <ui:VisualElement name="ApiKeyContainer" class="input-row">
                <ui:Label text="API Key:" class="input-label" />
                <ui:TextField name="ApiKeyField" password="true" class="input-field api-key-field" />
            </ui:VisualElement>

            <ui:VisualElement name="ModelContainer" class="input-row">
                <ui:Label text="Model:" class="input-label" />
                <ui:DropdownField name="ModelDropdown" class="input-field model-dropdown" />
                <ui:Button name="RefreshModelsButton" text="🔄" class="refresh-models-button" tooltip="Refresh models from Google API" />
            </ui:VisualElement>
        </ui:VisualElement>

        <!-- Template Section -->
        <ui:VisualElement name="TemplateSection" class="template-section">
            <ui:Label text="Prompt Templates" class="section-title" />

            <ui:VisualElement name="TemplateControls" class="template-controls">
                <ui:DropdownField name="TemplateDropdown" class="template-dropdown" />
                <ui:Button name="LoadTemplateButton" text="Load" class="template-button" />
                <ui:Button name="SaveTemplateButton" text="Save" class="template-button" />
            </ui:VisualElement>
        </ui:VisualElement>

        <!-- Prompt Section -->
        <ui:VisualElement name="PromptSection" class="prompt-section">
            <ui:VisualElement name="PromptHeader" class="prompt-header">
                <ui:Label text="UI Description" class="section-title" />
                <ui:Button name="EnhancePromptButton" text="✨ Enhance Prompt" class="enhance-prompt-button" tooltip="Use AI to enhance and improve your prompt" />
            </ui:VisualElement>
            <ui:TextField name="PromptField" multiline="true" class="prompt-field"
                         placeholder-text="Describe the UI you want to generate. Be specific about layout, components, styling, and behavior.&#10;&#10;Example: Create a login form with username and password fields, a login button, and a forgot password link. Use a modern card-style layout with rounded corners and subtle shadows." />
        </ui:VisualElement>

        <!-- Action Buttons -->
        <ui:VisualElement name="ActionSection" class="action-section">
            <ui:Button name="GenerateButton" text="Generate UI" class="action-button generate-button" />
            <ui:Button name="ApplyButton" text="Apply to Scene" class="action-button apply-button" />
        </ui:VisualElement>

        <!-- Status Section -->
        <ui:VisualElement name="StatusSection" class="status-section">
            <ui:Label name="StatusLabel" text="Ready" class="status-label" />
            <ui:ProgressBar name="ProgressBar" class="progress-bar" />
        </ui:VisualElement>

        <!-- Preview Section -->
        <ui:VisualElement name="PreviewSection" class="preview-section">
            <ui:VisualElement name="PreviewHeader" class="preview-header">
                <ui:Label text="Generated Code Preview" class="section-title" />
                <ui:Toggle name="PreviewToggle" text="Show Preview" value="true" class="preview-toggle" />
            </ui:VisualElement>

            <ui:ScrollView name="CodePreviewContainer" class="code-preview-container">
                <!-- Generated code preview will be inserted here -->
            </ui:ScrollView>
        </ui:VisualElement>

    </ui:VisualElement>
</ui:UXML>
