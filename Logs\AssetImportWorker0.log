Using pre-set license
Built from '6000.2/respin/6000.2.0b1-1618a92a88ac' branch; Version is '6000.2.0b1 (d17678da8412) revision 13727352'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'tr' Physical Memory: 65230 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-05-27T08:45:00Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.2.0b1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
C:/Users/<USER>/Animerge
-logFile
Logs/AssetImportWorker0.log
-srvPort
55674
-job-worker-count
11
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Users/<USER>/Animerge
C:/Users/<USER>/Animerge
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [35676]  Target information:

Player connection [35676]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2236682288 [EditorId] 2236682288 [Version] 1048832 [Id] WindowsEditor(7,EvPC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [35676]  * "[IP] ********** [Port] 0 [Flags] 2 [Guid] 2236682288 [EditorId] 2236682288 [Version] 1048832 [Id] WindowsEditor(7,EvPC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [35676] Host joined multi-casting on [***********:54997]...
Player connection [35676] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 11
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 3.78 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.2.0b1 (d17678da8412)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Animerge/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4090 (ID=0x2684)
    Vendor:   NVIDIA
    VRAM:     24142 MB
    Driver:   32.0.15.7640
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56100
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/PlaybackEngines/MetroSupport/UnityEditor.UWP.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.001973 seconds.
- Loaded All Assemblies, in  0.276 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 1270 ms
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.668 seconds
Domain Reload Profiling: 1942ms
	BeginReloadAssembly (91ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (113ms)
		LoadAssemblies (90ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (110ms)
			TypeCache.Refresh (109ms)
				TypeCache.ScanAssembly (100ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (1668ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1621ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1374ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (122ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.281 seconds
Refreshing native plugins compatible for Editor in 2.38 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001f94240c21e (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x000001f94240be33 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001f94240bb0b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001f94240b02e (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001f94240acca (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001f94240747b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x000001f942406cfb (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x000001f942406f05 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x000001f942228742 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001f94222861b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001f94124f433 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001f92f8153fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07bae0 (Unity) <lambda_dfe2fdae22f1629f164682966ac9a93e>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b45e (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001f94240c21e (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x000001f94240be33 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001f94240bb0b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001f94240b02e (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001f94240acca (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001f94124f64b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001f92f8153fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07bae0 (Unity) <lambda_dfe2fdae22f1629f164682966ac9a93e>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b45e (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.105 seconds
Domain Reload Profiling: 2382ms
	BeginReloadAssembly (134ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (21ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (1075ms)
		LoadAssemblies (633ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (518ms)
			TypeCache.Refresh (422ms)
				TypeCache.ScanAssembly (393ms)
			BuildScriptInfoCaches (82ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1105ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (968ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (176ms)
			ProcessInitializeOnLoadAttributes (560ms)
			ProcessInitializeOnLoadMethodAttributes (208ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 7.16 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 289 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8279 unused Assets / (13.3 MB). Loaded Objects now: 10877.
Memory consumption went from 205.8 MB to 192.5 MB.
Total: 11.292600 ms (FindLiveObjects: 0.855400 ms CreateObjectMapping: 0.925500 ms MarkObjects: 4.313200 ms  DeleteObjects: 5.197300 ms)

========================================================================
Received Import Request.
  Time since last request: 4026.402374 seconds.
  path: Assets/_Recovery
  artifactKey: Guid(1e3d992464a3ce749ad763136f97b0fa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/_Recovery using Guid(1e3d992464a3ce749ad763136f97b0fa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6aff5ea7cdc1dd755c9e376c8536d5ab') in 0.1186947 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.781 seconds
Refreshing native plugins compatible for Editor in 2.35 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001f942404ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x000001f942404cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001f9424049cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001f942403eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001f942403b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001f94240033b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x000001f9423ff1cb (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x000001f9423ff3d5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x000001f92f9c5ac2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001f92f9c599b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001f92f83bac3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001f94bd053fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001f942404ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x000001f942404cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001f9424049cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001f942403eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001f942403b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001f92f83bcdb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001f94bd053fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.772 seconds
Domain Reload Profiling: 1551ms
	BeginReloadAssembly (217ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (60ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (507ms)
		LoadAssemblies (366ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (212ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (193ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (772ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (626ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (181ms)
			ProcessInitializeOnLoadAttributes (323ms)
			ProcessInitializeOnLoadMethodAttributes (95ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 5.76 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8277 unused Assets / (13.3 MB). Loaded Objects now: 10882.
Memory consumption went from 177.6 MB to 164.3 MB.
Total: 12.961200 ms (FindLiveObjects: 0.885400 ms CreateObjectMapping: 1.043000 ms MarkObjects: 5.747500 ms  DeleteObjects: 5.283600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2942.044653 seconds.
  path: Assets/AI Toolkit/Temp
  artifactKey: Guid(5fd69e0706c43d54e97bb05d6095de9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AI Toolkit/Temp using Guid(5fd69e0706c43d54e97bb05d6095de9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f7873755cd7b9e3b18b95ceae2532a3e') in 0.0025498 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 32.900241 seconds.
  path: Assets/UI Toolkit/New Panel Settings.asset
  artifactKey: Guid(205fcc678107dc24c8efc495327e011d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI Toolkit/New Panel Settings.asset using Guid(205fcc678107dc24c8efc495327e011d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '170d8caccf50e2786a8d64455bc7049b') in 0.0481828 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.898931 seconds.
  path: Assets/UI Toolkit/New Panel Settings.asset
  artifactKey: Guid(205fcc678107dc24c8efc495327e011d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/UI Toolkit/New Panel Settings.asset using Guid(205fcc678107dc24c8efc495327e011d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ba4c96c885b4fefdc4b3c3b58d669f08') in 0.1663213 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.766 seconds
Refreshing native plugins compatible for Editor in 2.94 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001f942404ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x000001f942404cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001f9424049cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001f942403eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001f942403b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001f94240033b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x000001f9423ff7db (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x000001f9423ff9e5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x000001f93a995ac2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001f93a99599b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001f93a98bac3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001f94b0353fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001f942404ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x000001f942404cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001f9424049cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001f942403eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001f942403b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001f93a98bcdb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001f94b0353fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.812 seconds
Domain Reload Profiling: 1578ms
	BeginReloadAssembly (197ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (58ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (510ms)
		LoadAssemblies (366ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (216ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (196ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (812ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (649ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (195ms)
			ProcessInitializeOnLoadAttributes (329ms)
			ProcessInitializeOnLoadMethodAttributes (96ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 5.40 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8278 unused Assets / (13.3 MB). Loaded Objects now: 10887.
Memory consumption went from 177.8 MB to 164.5 MB.
Total: 14.773200 ms (FindLiveObjects: 1.335400 ms CreateObjectMapping: 0.788100 ms MarkObjects: 5.536600 ms  DeleteObjects: 7.111300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 57.016108 seconds.
  path: Assets/UI Toolkit/New Panel Settings.asset
  artifactKey: Guid(205fcc678107dc24c8efc495327e011d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/UI Toolkit/New Panel Settings.asset using Guid(205fcc678107dc24c8efc495327e011d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dbbfcc6a2f942f6ca96a461ef9458737') in 0.1919605 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 3.151966 seconds.
  path: Assets/UI Toolkit/New Panel Settings.asset
  artifactKey: Guid(205fcc678107dc24c8efc495327e011d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI Toolkit/New Panel Settings.asset using Guid(205fcc678107dc24c8efc495327e011d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8e71a62a328419ac0c97685aefb4b950') in 0.0171809 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 11.968330 seconds.
  path: Assets/Scenes/MainMenu.unity
  artifactKey: Guid(173324ceb87d31646a9e390ce82b4edd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scenes/MainMenu.unity using Guid(173324ceb87d31646a9e390ce82b4edd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9ec2a8acd8531439d3d1b4995995fcd2') in 0.0064594 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 5.344099 seconds.
  path: Assets/UI Toolkit/MainMenu.asset
  artifactKey: Guid(205fcc678107dc24c8efc495327e011d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI Toolkit/MainMenu.asset using Guid(205fcc678107dc24c8efc495327e011d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bd366b06907f1061be8cda77dbe9e324') in 0.0085449 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.917370 seconds.
  path: Assets/UI Toolkit/MainMenu.asset
  artifactKey: Guid(205fcc678107dc24c8efc495327e011d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/UI Toolkit/MainMenu.asset using Guid(205fcc678107dc24c8efc495327e011d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '658e9fb4769c4df4289cbc2331956b97') in 0.0105752 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 4.163946 seconds.
  path: Assets/UI Toolkit/gameUI.asset
  artifactKey: Guid(e3b7db387cc8e73419a0ea628935abd3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI Toolkit/gameUI.asset using Guid(e3b7db387cc8e73419a0ea628935abd3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e88f1ce63aa1097d3e4a580ec9633e4a') in 0.0137074 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.915909 seconds.
  path: Assets/UI Toolkit/gameUI.asset
  artifactKey: Guid(e3b7db387cc8e73419a0ea628935abd3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/UI Toolkit/gameUI.asset using Guid(e3b7db387cc8e73419a0ea628935abd3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5b1c9f4fdb4070b3f86893bff4f813d1') in 0.016883 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.766 seconds
Refreshing native plugins compatible for Editor in 3.42 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001f942404ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x000001f942404cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001f9424049cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001f942403eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001f942403b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001f94240033b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x000001f9423ff7db (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x000001f9423ff9e5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x000001f93a9b5ac2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001f93a9b599b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001f93a9abac3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001f94bda53fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001f942404ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x000001f942404cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001f9424049cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001f942403eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001f942403b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001f93a9abcdb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001f94bda53fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.785 seconds
Domain Reload Profiling: 1550ms
	BeginReloadAssembly (195ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (514ms)
		LoadAssemblies (365ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (221ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (201ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (785ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (631ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (190ms)
			ProcessInitializeOnLoadAttributes (319ms)
			ProcessInitializeOnLoadMethodAttributes (94ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 5.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8278 unused Assets / (11.7 MB). Loaded Objects now: 10889.
Memory consumption went from 177.9 MB to 166.2 MB.
Total: 12.986700 ms (FindLiveObjects: 0.935400 ms CreateObjectMapping: 1.125100 ms MarkObjects: 5.984900 ms  DeleteObjects: 4.940000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.848 seconds
Refreshing native plugins compatible for Editor in 2.88 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001f942404ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x000001f942404cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001f9424049cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001f942403eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001f942403b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001f94240033b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x000001f9423ff7db (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x000001f9423ff9e5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x000001f93a995ac2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001f93a99599b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001f93a98bac3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001f94afe53fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001f942404ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x000001f942404cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001f9424049cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001f942403eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001f942403b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001f93a98bcdb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001f94afe53fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.831 seconds
Domain Reload Profiling: 1679ms
	BeginReloadAssembly (204ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (582ms)
		LoadAssemblies (400ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (259ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (239ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (831ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (669ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (196ms)
			ProcessInitializeOnLoadAttributes (345ms)
			ProcessInitializeOnLoadMethodAttributes (102ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 4.58 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8277 unused Assets / (11.8 MB). Loaded Objects now: 10891.
Memory consumption went from 177.9 MB to 166.2 MB.
Total: 14.356400 ms (FindLiveObjects: 1.229600 ms CreateObjectMapping: 1.320300 ms MarkObjects: 6.460500 ms  DeleteObjects: 5.344500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.784 seconds
Refreshing native plugins compatible for Editor in 2.04 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001f942264ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x000001f942264cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001f9422649cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001f942263eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001f942263b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001f94226033b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x000001f94225f7db (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x000001f94225f9e5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x000001f92f9d5ac2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001f92f9d599b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001f92f9cbac3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001f94bc853fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001f942264ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x000001f942264cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001f9422649cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001f942263eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001f942263b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001f92f9cbcdb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001f94bc853fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.790 seconds
Domain Reload Profiling: 1574ms
	BeginReloadAssembly (189ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (538ms)
		LoadAssemblies (384ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (224ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (203ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (791ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (635ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (184ms)
			ProcessInitializeOnLoadAttributes (327ms)
			ProcessInitializeOnLoadMethodAttributes (98ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 4.96 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8277 unused Assets / (14.1 MB). Loaded Objects now: 10893.
Memory consumption went from 177.9 MB to 163.7 MB.
Total: 15.469400 ms (FindLiveObjects: 0.955600 ms CreateObjectMapping: 1.193300 ms MarkObjects: 5.885300 ms  DeleteObjects: 7.433200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.396 seconds
Refreshing native plugins compatible for Editor in 2.34 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001f942294ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x000001f942294cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001f9422949cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001f942293eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001f942293b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001f94229033b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x000001f94228f7db (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x000001f94228f9e5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x000001f93a9a5ac2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000001f93a9a599b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000001f93a99bac3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001f9423453fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000001f942294ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x000001f942294cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000001f9422949cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000001f942293eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000001f942293b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000001f93a99bcdb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000001f9423453fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.831 seconds
Domain Reload Profiling: 2226ms
	BeginReloadAssembly (447ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (61ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (136ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (877ms)
		LoadAssemblies (756ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (274ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (250ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (831ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (663ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (196ms)
			ProcessInitializeOnLoadAttributes (341ms)
			ProcessInitializeOnLoadMethodAttributes (98ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 2.44 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8277 unused Assets / (14.1 MB). Loaded Objects now: 10895.
Memory consumption went from 177.9 MB to 163.9 MB.
Total: 12.359500 ms (FindLiveObjects: 0.764500 ms CreateObjectMapping: 0.664000 ms MarkObjects: 4.830400 ms  DeleteObjects: 6.099400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 7361.639915 seconds.
  path: Assets/UI Toolkit/MainMenu.uss
  artifactKey: Guid(b5acdbb83fe4b694981332840ba78d4f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UI Toolkit/MainMenu.uss using Guid(b5acdbb83fe4b694981332840ba78d4f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b1bce700cdd5678ec747a4d02910ab45') in 0.0299872 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

