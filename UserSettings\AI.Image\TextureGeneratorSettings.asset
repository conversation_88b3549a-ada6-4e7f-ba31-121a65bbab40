%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 53
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0355ba069dae4d24bf2e318d15442628, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.AI.Image::Unity.AI.Image.Services.SessionPersistence.TextureGeneratorSettings
  m_Session:
    settings:
      lastSelectedModels:
        serializedData:
        - key: 0
          value:
            rid: 117101907428245992
        - key: 1
          value:
            rid: 117101907428245993
        - key: 2
          value:
            rid: 117101907428245994
        - key: 3
          value:
            rid: 117101907428245995
        - key: 4
          value:
            rid: 117101907428245996
        - key: 5
          value:
            rid: 117101907428245997
      previewSettings:
        sizeFactor: 1.71
  references:
    version: 2
    RefIds:
    - rid: 117101907428245992
      type: {class: ModelSelection, ns: Unity.AI.Image.Services.Stores.States, asm: Unity.AI.Image}
      data:
        modelID: 4dc55660-f3eb-42ba-93a4-6261d6b2b8ec
    - rid: 117101907428245993
      type: {class: ModelSelection, ns: Unity.AI.Image.Services.Stores.States, asm: Unity.AI.Image}
      data:
        modelID: 
    - rid: 117101907428245994
      type: {class: ModelSelection, ns: Unity.AI.Image.Services.Stores.States, asm: Unity.AI.Image}
      data:
        modelID: 
    - rid: 117101907428245995
      type: {class: ModelSelection, ns: Unity.AI.Image.Services.Stores.States, asm: Unity.AI.Image}
      data:
        modelID: 2d2d53fc-e209-4853-a358-738191781d9c
    - rid: 117101907428245996
      type: {class: ModelSelection, ns: Unity.AI.Image.Services.Stores.States, asm: Unity.AI.Image}
      data:
        modelID: 2d2d53fc-e209-4853-a358-738191781d9c
    - rid: 117101907428245997
      type: {class: ModelSelection, ns: Unity.AI.Image.Services.Stores.States, asm: Unity.AI.Image}
      data:
        modelID: 
