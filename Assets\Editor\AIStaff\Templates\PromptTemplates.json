{"templates": [{"name": "Login Form", "description": "A standard login form with username, password, and login button", "prompt": "Create a login form with username and password text fields, a login button, and a 'Forgot Password?' link. Use a modern card-style layout with rounded corners and subtle shadows. Include proper spacing and hover effects.", "category": "Forms", "useCount": 7, "tags": ["login", "form", "authentication"]}, {"name": "Settings Panel", "description": "A settings panel with various configuration options", "prompt": "Create a settings panel with sections for Audio, Graphics, and Controls. Include sliders for volume controls, dropdown menus for quality settings, and toggle switches for various options. Use a clean, organized layout with clear section headers.", "category": "Panels", "useCount": 1, "tags": ["settings", "configuration", "options"]}, {"name": "Inventory Grid", "description": "A grid-based inventory system for games", "prompt": "Create an inventory grid system with 6x8 slots. Each slot should be able to display an item icon and quantity. Include a search bar at the top and category tabs. Use a dark theme suitable for gaming interfaces.", "category": "Game UI", "useCount": 1, "tags": ["inventory", "grid", "game", "items"]}, {"name": "Dashboard Layout", "description": "A dashboard with statistics and charts", "prompt": "Create a dashboard layout with cards displaying various statistics. Include placeholder areas for charts, progress bars for metrics, and a sidebar with navigation options. Use a professional, clean design with good use of whitespace.", "category": "Layouts", "useCount": 2, "tags": ["dashboard", "statistics", "charts", "metrics"]}, {"name": "Chat Interface", "description": "A chat interface with message bubbles", "prompt": "Create a chat interface with a scrollable message area, text input field, and send button. Messages should appear as bubbles with different styles for sent and received messages. Include timestamps and user avatars.", "category": "Communication", "useCount": 1, "tags": ["chat", "messaging", "communication"]}], "version": 1}