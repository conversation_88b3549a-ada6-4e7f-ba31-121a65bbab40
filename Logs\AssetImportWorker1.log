Using pre-set license
Built from '6000.2/respin/6000.2.0b1-1618a92a88ac' branch; Version is '6000.2.0b1 (d17678da8412) revision 13727352'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'tr' Physical Memory: 65230 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-05-27T08:45:00Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.2.0b1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
C:/Users/<USER>/Animerge
-logFile
Logs/AssetImportWorker1.log
-srvPort
55674
-job-worker-count
11
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Users/<USER>/Animerge
C:/Users/<USER>/Animerge
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [24280]  Target information:

Player connection [24280]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 462850084 [EditorId] 462850084 [Version] 1048832 [Id] WindowsEditor(7,EvPC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [24280]  * "[IP] ********** [Port] 0 [Flags] 2 [Guid] 462850084 [EditorId] 462850084 [Version] 1048832 [Id] WindowsEditor(7,EvPC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [24280] Host joined multi-casting on [***********:54997]...
Player connection [24280] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 11
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 3.34 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.2.0b1 (d17678da8412)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Animerge/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4090 (ID=0x2684)
    Vendor:   NVIDIA
    VRAM:     24142 MB
    Driver:   32.0.15.7640
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56328
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/PlaybackEngines/MetroSupport/UnityEditor.UWP.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.001867 seconds.
- Loaded All Assemblies, in  0.279 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 1286 ms
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.668 seconds
Domain Reload Profiling: 1946ms
	BeginReloadAssembly (91ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (116ms)
		LoadAssemblies (90ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (113ms)
			TypeCache.Refresh (112ms)
				TypeCache.ScanAssembly (102ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (1669ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1632ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1394ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (70ms)
			ProcessInitializeOnLoadAttributes (119ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.255 seconds
Refreshing native plugins compatible for Editor in 4.07 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000002473893c21e (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x000002473893be33 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000002473893bb0b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000002473893b02e (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000002473893acca (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000002473893747b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x0000024738936cfb (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x0000024738936f05 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x00000247377882c2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000002473778819b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000002473777eab3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000024725d153fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07bae0 (Unity) <lambda_dfe2fdae22f1629f164682966ac9a93e>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b45e (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000002473893c21e (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x000002473893be33 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000002473893bb0b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000002473893b02e (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000002473893acca (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000002473777eccb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000024725d153fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07bae0 (Unity) <lambda_dfe2fdae22f1629f164682966ac9a93e>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b45e (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.121 seconds
Domain Reload Profiling: 2372ms
	BeginReloadAssembly (131ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (22ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (1051ms)
		LoadAssemblies (615ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (509ms)
			TypeCache.Refresh (413ms)
				TypeCache.ScanAssembly (382ms)
			BuildScriptInfoCaches (80ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1121ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (974ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (178ms)
			ProcessInitializeOnLoadAttributes (563ms)
			ProcessInitializeOnLoadMethodAttributes (208ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 4.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 289 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8279 unused Assets / (12.4 MB). Loaded Objects now: 10877.
Memory consumption went from 205.8 MB to 193.4 MB.
Total: 12.001700 ms (FindLiveObjects: 1.400700 ms CreateObjectMapping: 1.072100 ms MarkObjects: 5.268600 ms  DeleteObjects: 4.259000 ms)

========================================================================
Received Import Request.
  Time since last request: 4026.408809 seconds.
  path: Assets/_Recovery/0.unity
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/_Recovery/0.unity using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8851e2d61bbbdc3fbb79e7dbc237f4f8') in 0.1343372 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.765 seconds
Refreshing native plugins compatible for Editor in 2.48 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000024738934ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000024738934cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000247389349cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000024738933eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000024738933b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000002473893033b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x000002473892f1cb (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x000002473892f3d5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x0000024730f25ac2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x0000024730f2599b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x0000024730f1bac3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x00000247422d53fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000024738934ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000024738934cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000247389349cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000024738933eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000024738933b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000024730f1bcdb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x00000247422d53fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.785 seconds
Domain Reload Profiling: 1550ms
	BeginReloadAssembly (200ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (58ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (509ms)
		LoadAssemblies (371ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (210ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (192ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (785ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (626ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (181ms)
			ProcessInitializeOnLoadAttributes (326ms)
			ProcessInitializeOnLoadMethodAttributes (93ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 5.78 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8277 unused Assets / (14.2 MB). Loaded Objects now: 10882.
Memory consumption went from 177.5 MB to 163.3 MB.
Total: 13.742100 ms (FindLiveObjects: 0.853500 ms CreateObjectMapping: 1.043800 ms MarkObjects: 6.068200 ms  DeleteObjects: 5.775000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.766 seconds
Refreshing native plugins compatible for Editor in 3.01 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000024737734ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000024737734cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000247377349cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000024737733eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000024737733b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000002473773033b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x000002473772f1cb (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x000002473772f3d5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x0000024725cc5ac2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x0000024725cc599b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x0000024724cebac3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000002473e1953fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000024737734ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000024737734cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000247377349cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000024737733eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000024737733b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000024724cebcdb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000002473e1953fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.823 seconds
Domain Reload Profiling: 1588ms
	BeginReloadAssembly (188ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (519ms)
		LoadAssemblies (376ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (216ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (196ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (824ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (664ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (205ms)
			ProcessInitializeOnLoadAttributes (330ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 5.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8277 unused Assets / (13.0 MB). Loaded Objects now: 10884.
Memory consumption went from 177.7 MB to 164.7 MB.
Total: 10.517000 ms (FindLiveObjects: 0.710100 ms CreateObjectMapping: 0.684100 ms MarkObjects: 4.239700 ms  DeleteObjects: 4.881800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 3033.168518 seconds.
  path: Assets/Scenes/MainMenu.unity
  artifactKey: Guid(173324ceb87d31646a9e390ce82b4edd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scenes/MainMenu.unity using Guid(173324ceb87d31646a9e390ce82b4edd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd258651d3d0c396a3963bed8ff7c5c5b') in 0.1232497 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.759 seconds
Refreshing native plugins compatible for Editor in 3.42 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000024738934ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000024738934cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000247389349cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000024738933eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000024738933b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000002473893033b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x000002473892f7db (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x000002473892f9e5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x00000247439e5ac2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x00000247439e599b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x00000247439dbac3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x00000247388553fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000024738934ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000024738934cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000247389349cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000024738933eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000024738933b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x00000247439dbcdb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x00000247388553fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.792 seconds
Domain Reload Profiling: 1551ms
	BeginReloadAssembly (193ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (509ms)
		LoadAssemblies (359ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (221ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (201ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (792ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (632ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (190ms)
			ProcessInitializeOnLoadAttributes (316ms)
			ProcessInitializeOnLoadMethodAttributes (97ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 4.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8277 unused Assets / (12.8 MB). Loaded Objects now: 10886.
Memory consumption went from 177.7 MB to 164.9 MB.
Total: 11.736100 ms (FindLiveObjects: 0.849000 ms CreateObjectMapping: 1.080700 ms MarkObjects: 4.637800 ms  DeleteObjects: 5.167700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.844 seconds
Refreshing native plugins compatible for Editor in 2.75 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000024738924ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000024738924cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000247389249cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000024738923eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000024738923b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000002473892033b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x000002473891f1cb (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x000002473891f3d5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x0000024737765ac2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x000002473776599b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x000002473775bac3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x00000247233053fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000024738924ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000024738924cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000247389249cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000024738923eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000024738923b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000002473775bcdb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x00000247233053fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.828 seconds
Domain Reload Profiling: 1672ms
	BeginReloadAssembly (202ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (579ms)
		LoadAssemblies (399ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (257ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (237ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (829ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (667ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (197ms)
			ProcessInitializeOnLoadAttributes (344ms)
			ProcessInitializeOnLoadMethodAttributes (100ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 5.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8277 unused Assets / (13.1 MB). Loaded Objects now: 10888.
Memory consumption went from 177.8 MB to 164.7 MB.
Total: 15.142400 ms (FindLiveObjects: 0.853100 ms CreateObjectMapping: 1.054600 ms MarkObjects: 6.752100 ms  DeleteObjects: 6.480300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.791 seconds
Refreshing native plugins compatible for Editor in 1.93 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000024737804ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000024737804cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000247378049cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000024737803eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000024737803b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000002473780033b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x00000247377ff1cb (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x00000247377ff3d5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x0000024730f05ac2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x0000024730f0599b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x0000024730efbac3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000002473f1553fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000024737804ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000024737804cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000247378049cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000024737803eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000024737803b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000024730efbcdb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000002473f1553fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.797 seconds
Domain Reload Profiling: 1586ms
	BeginReloadAssembly (189ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (542ms)
		LoadAssemblies (386ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (228ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (207ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (797ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (641ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (184ms)
			ProcessInitializeOnLoadAttributes (330ms)
			ProcessInitializeOnLoadMethodAttributes (99ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 4.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8277 unused Assets / (13.3 MB). Loaded Objects now: 10890.
Memory consumption went from 177.9 MB to 164.6 MB.
Total: 14.404200 ms (FindLiveObjects: 0.856700 ms CreateObjectMapping: 1.197100 ms MarkObjects: 6.908600 ms  DeleteObjects: 5.439700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.397 seconds
Refreshing native plugins compatible for Editor in 2.52 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000024738874ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000024738874cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000247388749cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000024738873eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000024738873b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x000002473887033b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x000002473885f1cb (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x000002473885f3d5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x0000024730f05ac2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x0000024730f0599b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x0000024730efbac3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000024738e153fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000024738874ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000024738874cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000247388749cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000024738873eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000024738873b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000024730efbcdb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000024738e153fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.828 seconds
Domain Reload Profiling: 2224ms
	BeginReloadAssembly (448ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (61ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (134ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (877ms)
		LoadAssemblies (756ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (274ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (250ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (828ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (662ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (194ms)
			ProcessInitializeOnLoadAttributes (346ms)
			ProcessInitializeOnLoadMethodAttributes (94ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 2.47 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8277 unused Assets / (12.5 MB). Loaded Objects now: 10892.
Memory consumption went from 177.8 MB to 165.3 MB.
Total: 11.051200 ms (FindLiveObjects: 0.740400 ms CreateObjectMapping: 0.687700 ms MarkObjects: 4.606400 ms  DeleteObjects: 5.015400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 4.52 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8261 unused Assets / (12.2 MB). Loaded Objects now: 10899.
Memory consumption went from 178.1 MB to 165.8 MB.
Total: 23.277300 ms (FindLiveObjects: 1.945600 ms CreateObjectMapping: 0.770000 ms MarkObjects: 14.800900 ms  DeleteObjects: 5.758800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 22651.366978 seconds.
  path: Assets/Editor/AIStaff/GeminiAPIClient.cs
  artifactKey: Guid(b4848c58dd15f25419dd3e1005372a70) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Editor/AIStaff/GeminiAPIClient.cs using Guid(b4848c58dd15f25419dd3e1005372a70) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '38df89e946f475a79e2375fbbc1befda') in 0.2639047 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/Editor/AIStaff/GeminiUIGeneratorWindow.uss
  artifactKey: Guid(25fd12c914dd113438f4ec87f0b7f7ce) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Editor/AIStaff/GeminiUIGeneratorWindow.uss using Guid(25fd12c914dd113438f4ec87f0b7f7ce) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7563b15a6bf857a739820b49618f70ea') in 0.0299562 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Editor/AIStaff/MenuItems.cs
  artifactKey: Guid(665b570e763b0d1468e2cba26b962054) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Editor/AIStaff/MenuItems.cs using Guid(665b570e763b0d1468e2cba26b962054) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2c3e98be882a84a58fd8b460fc283675') in 0.0074686 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/Editor/AIStaff/UICodeGenerator.cs
  artifactKey: Guid(88aab4f556fa52f42a3e8c37cf08c75e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Editor/AIStaff/UICodeGenerator.cs using Guid(88aab4f556fa52f42a3e8c37cf08c75e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f24acecb729f620bc0eeb06b4500f37f') in 0.0043781 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/Editor/AIStaff/GeminiUIGeneratorWindow.uxml
  artifactKey: Guid(db3e22cb339c6e748b39ac306fe68ddd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Editor/AIStaff/GeminiUIGeneratorWindow.uxml using Guid(db3e22cb339c6e748b39ac306fe68ddd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '30115c63ec145748c365054a7e8cffd9') in 0.0892067 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 4.76 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8261 unused Assets / (13.5 MB). Loaded Objects now: 10899.
Memory consumption went from 178.7 MB to 165.1 MB.
Total: 16.774000 ms (FindLiveObjects: 0.937600 ms CreateObjectMapping: 1.212600 ms MarkObjects: 6.807800 ms  DeleteObjects: 7.814300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.52 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8261 unused Assets / (13.7 MB). Loaded Objects now: 10899.
Memory consumption went from 178.6 MB to 164.9 MB.
Total: 17.706000 ms (FindLiveObjects: 0.938600 ms CreateObjectMapping: 1.121900 ms MarkObjects: 7.331900 ms  DeleteObjects: 8.312100 ms)

Prepare: number of updated asset objects reloaded= 0
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0