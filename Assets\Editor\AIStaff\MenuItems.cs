using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;

namespace AIStaff.UIGenerator
{
    public static class MenuItems
    {
        private const string MENU_ROOT = "AI Staff/";
        private const string WINDOW_MENU_ROOT = "Window/AI Staff/";
        private const string ASSETS_MENU_ROOT = "Assets/Create/AI Staff/";

        #region Window Menu Items

        [MenuItem(WINDOW_MENU_ROOT + "Gemini UI Generator", false, 1)]
        public static void OpenGeminiUIGenerator()
        {
            GeminiUIGeneratorWindow.ShowWindow();
        }

        [MenuItem(WINDOW_MENU_ROOT + "UI Generator Settings", false, 2)]
        public static void OpenSettings()
        {
            var settings = GeminiUIGeneratorSettings.GetOrCreateSettings();
            Selection.activeObject = settings;
            EditorGUIUtility.PingObject(settings);
        }

        #endregion

        #region Main Menu Items

        [MenuItem(MENU_ROOT + "Open UI Generator", false, 1)]
        public static void OpenUIGeneratorFromMainMenu()
        {
            GeminiUIGeneratorWindow.ShowWindow();
        }

        [MenuItem(MENU_ROOT + "Quick Generate UI", false, 2)]
        public static void QuickGenerateUI()
        {
            GeminiUIGeneratorWindow window = GeminiUIGeneratorWindow.ShowWindowInternal();
            // Focus on the prompt field for quick input
            EditorApplication.delayCall += () =>
            {
                if (window != null)
                {
                    var promptField = window.rootVisualElement.Q<TextField>("PromptField");
                    promptField?.Focus();
                }
            };
        }

        [MenuItem(MENU_ROOT + "Settings", false, 11)]
        public static void OpenSettingsFromMainMenu()
        {
            OpenSettings();
        }

        [MenuItem(MENU_ROOT + "Documentation", false, 21)]
        public static void OpenDocumentation()
        {
            Application.OpenURL("https://ai.google.dev/gemini-api/docs");
        }

        [MenuItem(MENU_ROOT + "Report Issue", false, 22)]
        public static void ReportIssue()
        {
            Application.OpenURL("https://github.com/your-repo/issues");
        }

        #endregion

        #region Assets Menu Items

        [MenuItem(ASSETS_MENU_ROOT + "Generate UI from Selection", false, 1)]
        public static void GenerateUIFromSelection()
        {
            var selectedObjects = Selection.objects;
            if (selectedObjects.Length == 0)
            {
                EditorUtility.DisplayDialog("No Selection",
                    "Please select a GameObject to apply the generated UI to.", "OK");
                return;
            }

            GeminiUIGeneratorWindow window = GeminiUIGeneratorWindow.ShowWindowInternal();
            // Set a default prompt based on selection
            EditorApplication.delayCall += () =>
            {
                if (window != null)
                {
                    var promptField = window.rootVisualElement.Q<TextField>("PromptField");
                    if (promptField != null && string.IsNullOrEmpty(promptField.value))
                    {
                        promptField.value = $"Create a UI for the selected GameObject: {selectedObjects[0].name}";
                    }
                }
            };
        }

        [MenuItem(ASSETS_MENU_ROOT + "Generate UI from Selection", true)]
        public static bool ValidateGenerateUIFromSelection()
        {
            return Selection.activeGameObject != null;
        }

        [MenuItem(ASSETS_MENU_ROOT + "Create UI Document with AI", false, 2)]
        public static void CreateUIDocumentWithAI()
        {
            GeminiUIGeneratorWindow window = GeminiUIGeneratorWindow.ShowWindowInternal();
            EditorApplication.delayCall += () =>
            {
                if (window != null)
                {
                    var promptField = window.rootVisualElement.Q<TextField>("PromptField");
                    if (promptField != null && string.IsNullOrEmpty(promptField.value))
                    {
                        promptField.value = "Create a new UI document with...";
                        promptField.Focus();
                        // Select the placeholder text for easy replacement
                        promptField.SelectAll();
                    }
                }
            };
        }

        #endregion

        #region Context Menu Items

        [MenuItem("GameObject/AI Staff/Add Generated UI", false, 10)]
        public static void AddGeneratedUIToGameObject()
        {
            var selectedObject = Selection.activeGameObject;
            if (selectedObject == null)
            {
                EditorUtility.DisplayDialog("No GameObject Selected",
                    "Please select a GameObject to add the UI to.", "OK");
                return;
            }

            GeminiUIGeneratorWindow window = GeminiUIGeneratorWindow.ShowWindowInternal();
            EditorApplication.delayCall += () =>
            {
                if (window != null)
                {
                    var promptField = window.rootVisualElement.Q<TextField>("PromptField");
                    if (promptField != null)
                    {
                        promptField.value = $"Create a UI interface for {selectedObject.name}";
                        promptField.Focus();
                    }
                }
            };
        }

        [MenuItem("GameObject/AI Staff/Add Generated UI", true)]
        public static bool ValidateAddGeneratedUIToGameObject()
        {
            return Selection.activeGameObject != null;
        }

        [MenuItem("CONTEXT/UIDocument/Generate New UI", false, 1000)]
        public static void GenerateNewUIForDocument(MenuCommand command)
        {
            var uiDocument = command.context as UIDocument;
            if (uiDocument == null) return;

            GeminiUIGeneratorWindow window = GeminiUIGeneratorWindow.ShowWindowInternal();
            EditorApplication.delayCall += () =>
            {
                if (window != null)
                {
                    var promptField = window.rootVisualElement.Q<TextField>("PromptField");
                    if (promptField != null)
                    {
                        promptField.value = $"Replace the UI for {uiDocument.gameObject.name} with...";
                        promptField.Focus();
                    }
                }
            };
        }

        #endregion

        #region Utility Menu Items

        [MenuItem(MENU_ROOT + "Utilities/Clear Generated UI Cache", false, 31)]
        public static void ClearGeneratedUICache()
        {
            if (EditorUtility.DisplayDialog("Clear Cache",
                "This will delete all generated UI files. Are you sure?",
                "Clear", "Cancel"))
            {
                try
                {
                    var generatedFolder = "Assets/UI Toolkit/Generated";
                    if (AssetDatabase.IsValidFolder(generatedFolder))
                    {
                        AssetDatabase.DeleteAsset(generatedFolder);
                        AssetDatabase.Refresh();
                        Debug.Log("Generated UI cache cleared successfully.");
                    }
                    else
                    {
                        Debug.Log("No generated UI cache found.");
                    }
                }
                catch (System.Exception ex)
                {
                    Debug.LogError($"Failed to clear cache: {ex.Message}");
                }
            }
        }

        [MenuItem(MENU_ROOT + "Utilities/Export Templates", false, 32)]
        public static void ExportTemplates()
        {
            var templateManager = new PromptTemplateManager();
            string exportPath = EditorUtility.SaveFilePanel(
                "Export Prompt Templates",
                "",
                "prompt_templates",
                "json"
            );

            if (!string.IsNullOrEmpty(exportPath))
            {
                try
                {
                    templateManager.ExportTemplates(exportPath);
                    EditorUtility.DisplayDialog("Export Successful",
                        $"Templates exported to:\n{exportPath}", "OK");
                }
                catch (System.Exception ex)
                {
                    EditorUtility.DisplayDialog("Export Failed",
                        $"Failed to export templates:\n{ex.Message}", "OK");
                }
            }
        }

        [MenuItem(MENU_ROOT + "Utilities/Import Templates", false, 33)]
        public static void ImportTemplates()
        {
            string importPath = EditorUtility.OpenFilePanel(
                "Import Prompt Templates",
                "",
                "json"
            );

            if (!string.IsNullOrEmpty(importPath))
            {
                try
                {
                    var templateManager = new PromptTemplateManager();
                    templateManager.ImportTemplates(importPath);
                    EditorUtility.DisplayDialog("Import Successful",
                        "Templates imported successfully!", "OK");
                }
                catch (System.Exception ex)
                {
                    EditorUtility.DisplayDialog("Import Failed",
                        $"Failed to import templates:\n{ex.Message}", "OK");
                }
            }
        }

        [MenuItem(MENU_ROOT + "Utilities/Reset All Settings", false, 34)]
        public static void ResetAllSettings()
        {
            if (EditorUtility.DisplayDialog("Reset Settings",
                "This will reset all Gemini UI Generator settings to their default values. Are you sure?",
                "Reset", "Cancel"))
            {
                var settings = GeminiUIGeneratorSettings.GetOrCreateSettings();
                settings.ResetToDefaults();
                EditorUtility.DisplayDialog("Settings Reset",
                    "All settings have been reset to their default values.", "OK");
            }
        }

        #endregion

        #region Help Menu Items

        [MenuItem(MENU_ROOT + "Help/Getting Started", false, 41)]
        public static void ShowGettingStarted()
        {
            var helpText = @"Gemini UI Generator - Getting Started

1. Get your Gemini API key from Google AI Studio
2. Enter your API key in the settings
3. Describe the UI you want to create
4. Click 'Generate UI' to create UXML and USS code
5. Apply the generated UI to your scene

Tips:
- Be specific in your descriptions
- Use the built-in templates for common UI patterns
- Preview the generated code before applying
- Save your own templates for reuse

For more help, visit the documentation.";

            EditorUtility.DisplayDialog("Getting Started", helpText, "OK");
        }

        [MenuItem(MENU_ROOT + "Help/Keyboard Shortcuts", false, 42)]
        public static void ShowKeyboardShortcuts()
        {
            var shortcutsText = @"Keyboard Shortcuts

Ctrl+Shift+G - Open Gemini UI Generator
Ctrl+G - Quick Generate (focus prompt field)
Ctrl+Enter - Generate UI (when in prompt field)
Ctrl+Shift+A - Apply generated UI
Ctrl+S - Save current prompt as template
Ctrl+L - Load template

Note: Some shortcuts may vary based on your system.";

            EditorUtility.DisplayDialog("Keyboard Shortcuts", shortcutsText, "OK");
        }

        [MenuItem(MENU_ROOT + "Help/About", false, 43)]
        public static void ShowAbout()
        {
            var aboutText = @"Gemini UI Generator v1.0

A comprehensive Unity Editor tool for generating
UI Toolkit interfaces using Google's Gemini AI.

Features:
- AI-powered UXML/USS generation
- Template system for common patterns
- Live code preview with syntax highlighting
- Seamless Unity integration
- Undo/redo support

Developed for Unity 6+ with UI Toolkit.

© 2024 AI Staff Tools";

            EditorUtility.DisplayDialog("About Gemini UI Generator", aboutText, "OK");
        }

        #endregion

        #region Validation Methods

        [MenuItem(MENU_ROOT + "Quick Generate UI", true)]
        public static bool ValidateQuickGenerateUI()
        {
            var settings = GeminiUIGeneratorSettings.GetOrCreateSettings();
            return settings.IsAPIKeyValid();
        }

        #endregion

        #region Keyboard Shortcuts

        [MenuItem(MENU_ROOT + "Open UI Generator %#g", false, 1)]
        public static void OpenUIGeneratorShortcut()
        {
            OpenGeminiUIGenerator();
        }

        [MenuItem(MENU_ROOT + "Quick Generate UI %g", false, 2)]
        public static void QuickGenerateUIShortcut()
        {
            QuickGenerateUI();
        }

        #endregion
    }

    // Additional shortcuts for window-specific actions
    public static class GeminiUIGeneratorShortcuts
    {
        [MenuItem(MENU_ROOT + "Generate UI (Window Context) %#RETURN", false, 100)]
        public static void GenerateUIShortcut()
        {
            var window = EditorWindow.focusedWindow as GeminiUIGeneratorWindow;
            if (window != null)
            {
                var generateButton = window.rootVisualElement.Q<Button>("GenerateButton");
                if (generateButton != null && generateButton.enabledSelf)
                {
                    generateButton.clicked?.Invoke();
                }
            }
        }

        [MenuItem(MENU_ROOT + "Generate UI (Window Context) %#RETURN", true)]
        public static bool ValidateGenerateUIShortcut()
        {
            return EditorWindow.focusedWindow is GeminiUIGeneratorWindow;
        }

        [MenuItem(MENU_ROOT + "Apply UI (Window Context) %#a", false, 101)]
        public static void ApplyUIShortcut()
        {
            var window = EditorWindow.focusedWindow as GeminiUIGeneratorWindow;
            if (window != null)
            {
                var applyButton = window.rootVisualElement.Q<Button>("ApplyButton");
                if (applyButton != null && applyButton.enabledSelf)
                {
                    applyButton.clicked?.Invoke();
                }
            }
        }

        [MenuItem(MENU_ROOT + "Apply UI (Window Context) %#a", true)]
        public static bool ValidateApplyUIShortcut()
        {
            var window = EditorWindow.focusedWindow as GeminiUIGeneratorWindow;
            if (window != null)
            {
                var applyButton = window.rootVisualElement.Q<Button>("ApplyButton");
                return applyButton != null && applyButton.enabledSelf;
            }
            return false;
        }

        [MenuItem(MENU_ROOT + "Save Template (Window Context) %#s", false, 102)]
        public static void SaveTemplateShortcut()
        {
            var window = EditorWindow.focusedWindow as GeminiUIGeneratorWindow;
            if (window != null)
            {
                var saveButton = window.rootVisualElement.Q<Button>("SaveTemplateButton");
                if (saveButton != null)
                {
                    saveButton.clicked?.Invoke();
                }
            }
        }

        [MenuItem(MENU_ROOT + "Save Template (Window Context) %#s", true)]
        public static bool ValidateSaveTemplateShortcut()
        {
            return EditorWindow.focusedWindow is GeminiUIGeneratorWindow;
        }
    }
}
