using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using UnityEngine;
using UnityEngine.TextCore.Text;
using UnityEngine.UIElements;

namespace AIStaff.UIGenerator
{
    public class UIPreviewRenderer
    {
        private readonly Dictionary<string, Color> syntaxColors = new Dictionary<string, Color>
        {
            { "tag", new Color(0.34f, 0.61f, 0.85f) },        // #569cd6 - Blue for tags
            { "attribute", new Color(0.61f, 0.86f, 0.99f) },  // #9cdcfe - Light blue for attributes
            { "string", new Color(0.81f, 0.57f, 0.47f) },     // #ce9178 - Orange for strings
            { "comment", new Color(0.42f, 0.60f, 0.33f) },    // #6a9955 - Green for comments
            { "property", new Color(0.61f, 0.86f, 0.99f) },   // #9cdcfe - Light blue for CSS properties
            { "value", new Color(0.81f, 0.57f, 0.47f) },      // #ce9178 - Orange for CSS values
            { "selector", new Color(0.84f, 0.73f, 0.49f) },   // #d7ba7d - Yellow for CSS selectors
            { "default", new Color(0.83f, 0.83f, 0.83f) }     // #d4d4d4 - Light gray for default text
        };

        public VisualElement CreatePreview(string uxmlCode, string ussCode)
        {
            var container = new VisualElement();
            container.AddToClassList("code-preview-container");

            // Create UXML preview
            if (!string.IsNullOrEmpty(uxmlCode))
            {
                var uxmlBlock = CreateCodeBlock("UXML", uxmlCode, CodeType.UXML);
                container.Add(uxmlBlock);
            }

            // Create USS preview
            if (!string.IsNullOrEmpty(ussCode))
            {
                var ussBlock = CreateCodeBlock("USS", ussCode, CodeType.USS);
                container.Add(ussBlock);
            }

            return container;
        }

        private VisualElement CreateCodeBlock(string title, string code, CodeType codeType)
        {
            var codeBlock = new VisualElement();
            codeBlock.AddToClassList("code-block");

            // Header
            var header = new VisualElement();
            header.AddToClassList("code-header");

            var titleLabel = new Label(title);
            titleLabel.AddToClassList("code-title");
            header.Add(titleLabel);

            // Copy button
            var copyButton = new Button(() => CopyToClipboard(code))
            {
                text = "Copy"
            };
            copyButton.style.position = Position.Absolute;
            copyButton.style.right = 8;
            copyButton.style.top = 4;
            copyButton.style.height = 20;
            copyButton.style.fontSize = 10;
            header.Add(copyButton);

            codeBlock.Add(header);

            // Content
            var content = new ScrollView();
            content.AddToClassList("code-content");
            content.horizontalScrollerVisibility = ScrollerVisibility.Auto;
            content.verticalScrollerVisibility = ScrollerVisibility.Auto;

            var syntaxHighlightedCode = ApplySyntaxHighlighting(code, codeType);
            content.Add(syntaxHighlightedCode);

            codeBlock.Add(content);

            return codeBlock;
        }

        private VisualElement ApplySyntaxHighlighting(string code, CodeType codeType)
        {
            var container = new VisualElement();
            
            switch (codeType)
            {
                case CodeType.UXML:
                    return HighlightUXML(code);
                case CodeType.USS:
                    return HighlightUSS(code);
                default:
                    return CreatePlainTextElement(code);
            }
        }

        private VisualElement HighlightUXML(string code)
        {
            var container = new VisualElement();
            var lines = code.Split('\n');

            foreach (var line in lines)
            {
                var lineElement = new VisualElement();
                lineElement.style.flexDirection = FlexDirection.Row;
                lineElement.style.flexWrap = Wrap.Wrap;

                if (string.IsNullOrWhiteSpace(line))
                {
                    lineElement.Add(new Label("\n"));
                }
                else
                {
                    ProcessUXMLLine(line, lineElement);
                }

                container.Add(lineElement);
            }

            return container;
        }

        private void ProcessUXMLLine(string line, VisualElement lineElement)
        {
            // Regex patterns for UXML syntax highlighting
            var patterns = new List<(Regex regex, string colorKey)>
            {
                (new Regex(@"<!--.*?-->"), "comment"),
                (new Regex(@"</?[\w:]+"), "tag"),
                (new Regex(@"\s+[\w:-]+(?==)"), "attribute"),
                (new Regex(@"""[^""]*"""), "string"),
                (new Regex(@"'[^']*'"), "string")
            };

            var segments = new List<(int start, int length, string colorKey)>();
            
            // Find all matches
            foreach (var (regex, colorKey) in patterns)
            {
                var matches = regex.Matches(line);
                foreach (Match match in matches)
                {
                    segments.Add((match.Index, match.Length, colorKey));
                }
            }

            // Sort segments by position
            segments.Sort((a, b) => a.start.CompareTo(b.start));

            // Create text elements
            int currentPos = 0;
            foreach (var segment in segments)
            {
                // Add text before this segment
                if (segment.start > currentPos)
                {
                    var beforeText = line.Substring(currentPos, segment.start - currentPos);
                    if (!string.IsNullOrEmpty(beforeText))
                    {
                        lineElement.Add(CreateColoredLabel(beforeText, "default"));
                    }
                }

                // Add the highlighted segment
                var segmentText = line.Substring(segment.start, segment.length);
                lineElement.Add(CreateColoredLabel(segmentText, segment.colorKey));

                currentPos = segment.start + segment.length;
            }

            // Add remaining text
            if (currentPos < line.Length)
            {
                var remainingText = line.Substring(currentPos);
                lineElement.Add(CreateColoredLabel(remainingText, "default"));
            }

            // If no segments were found, add the whole line
            if (segments.Count == 0)
            {
                lineElement.Add(CreateColoredLabel(line, "default"));
            }

            lineElement.Add(new Label("\n"));
        }

        private VisualElement HighlightUSS(string code)
        {
            var container = new VisualElement();
            var lines = code.Split('\n');

            foreach (var line in lines)
            {
                var lineElement = new VisualElement();
                lineElement.style.flexDirection = FlexDirection.Row;
                lineElement.style.flexWrap = Wrap.Wrap;

                if (string.IsNullOrWhiteSpace(line))
                {
                    lineElement.Add(new Label("\n"));
                }
                else
                {
                    ProcessUSSLine(line, lineElement);
                }

                container.Add(lineElement);
            }

            return container;
        }

        private void ProcessUSSLine(string line, VisualElement lineElement)
        {
            var trimmedLine = line.Trim();

            // Comments
            if (trimmedLine.StartsWith("/*") || trimmedLine.StartsWith("//"))
            {
                lineElement.Add(CreateColoredLabel(line, "comment"));
                lineElement.Add(new Label("\n"));
                return;
            }

            // Selectors (lines ending with { or standalone selectors)
            if (trimmedLine.EndsWith("{") || (!trimmedLine.Contains(":") && !trimmedLine.Contains("}")))
            {
                lineElement.Add(CreateColoredLabel(line, "selector"));
                lineElement.Add(new Label("\n"));
                return;
            }

            // Properties and values
            if (trimmedLine.Contains(":"))
            {
                var colonIndex = line.IndexOf(':');
                var beforeColon = line.Substring(0, colonIndex);
                var afterColon = line.Substring(colonIndex);

                lineElement.Add(CreateColoredLabel(beforeColon, "property"));
                lineElement.Add(CreateColoredLabel(":", "default"));

                // Highlight values
                var valueMatch = Regex.Match(afterColon, @":\s*([^;]+)");
                if (valueMatch.Success)
                {
                    var beforeValue = afterColon.Substring(0, valueMatch.Groups[1].Index - 1);
                    var value = valueMatch.Groups[1].Value;
                    var afterValue = afterColon.Substring(valueMatch.Groups[1].Index + valueMatch.Groups[1].Length - 1);

                    lineElement.Add(CreateColoredLabel(beforeValue, "default"));
                    lineElement.Add(CreateColoredLabel(value, "value"));
                    lineElement.Add(CreateColoredLabel(afterValue, "default"));
                }
                else
                {
                    lineElement.Add(CreateColoredLabel(afterColon.Substring(1), "default"));
                }

                lineElement.Add(new Label("\n"));
                return;
            }

            // Default case
            lineElement.Add(CreateColoredLabel(line, "default"));
            lineElement.Add(new Label("\n"));
        }

        private Label CreateColoredLabel(string text, string colorKey)
        {
            var label = new Label(text);
            
            if (syntaxColors.TryGetValue(colorKey, out Color color))
            {
                label.style.color = color;
            }
            else
            {
                label.style.color = syntaxColors["default"];
            }

            label.style.whiteSpace = WhiteSpace.PreWrap;
            label.style.fontSize = 11;
            //label.style.fontFamily = new FontDefinition(FontAsset.defaultFontAsset);
            
            return label;
        }

        private VisualElement CreatePlainTextElement(string code)
        {
            var label = new Label(code);
            label.style.color = syntaxColors["default"];
            label.style.whiteSpace = WhiteSpace.PreWrap;
            label.style.fontSize = 11;
            //label.style.fontFamily = new FontDefinition(FontAsset.defaultFontAsset);
            return label;
        }

        private void CopyToClipboard(string text)
        {
            try
            {
                GUIUtility.systemCopyBuffer = text;
                Debug.Log("Code copied to clipboard");
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to copy to clipboard: {ex.Message}");
            }
        }

        private string ExtractUIStructureInfo(string uxmlCode)
        {
            var elements = new List<string>();
            var matches = Regex.Matches(uxmlCode, @"<(ui:)?(\w+)");
            
            foreach (Match match in matches)
            {
                string elementName = match.Groups[2].Value;
                if (!elements.Contains(elementName) && elementName != "UXML" && elementName != "Style")
                {
                    elements.Add(elementName);
                }
            }
            
            return string.Join(", ", elements);
        }

        private enum CodeType
        {
            UXML,
            USS,
            Plain
        }
    }
}
