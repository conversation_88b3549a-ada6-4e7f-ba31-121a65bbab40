/* Root Container */
.root-container {
    flex-grow: 1;
    padding: 16px;
    background-color: var(--unity-colors-window-background);
}

/* Header Section */
.header-section {
    margin-bottom: 20px;
    padding: 16px;
    background-color: var(--unity-colors-inspector_titlebar-background);
    border-radius: 8px;
    border-width: 1px;
    border-color: var(--unity-colors-inspector_titlebar-border);
}

.header-title {
    font-size: 24px;
    -unity-font-style: bold;
    color: var(--unity-colors-default-text);
    margin-bottom: 4px;
}

.header-subtitle {
    font-size: 14px;
    color: var(--unity-colors-default-text_75);
    -unity-font-style: italic;
}

/* Section Styling */
.settings-section,
.template-section,
.prompt-section,
.action-section,
.status-section,
.preview-section {
    margin-bottom: 16px;
    padding: 12px;
    background-color: var(--unity-colors-inspector_titlebar-background);
    border-radius: 6px;
    border-width: 1px;
    border-color: var(--unity-colors-inspector_titlebar-border);
}

.section-title {
    font-size: 16px;
    -unity-font-style: bold;
    color: var(--unity-colors-default-text);
    margin-bottom: 8px;
}

/* Input Rows */
.input-row {
    flex-direction: row;
    align-items: center;
    margin-bottom: 8px;
}

.input-label {
    width: 80px;
    min-width: 80px;
    color: var(--unity-colors-default-text);
    margin-right: 8px;
}

.input-field {
    flex-grow: 1;
    min-height: 20px;
}

.api-key-field {
    background-color: var(--unity-colors-input_field-background);
    border-color: var(--unity-colors-input_field-border);
    border-radius: 4px;
}

.model-dropdown {
    flex-grow: 1;
    background-color: var(--unity-colors-dropdown-background);
    border-color: var(--unity-colors-dropdown-border);
    border-radius: 4px;
}

.refresh-models-button {
    width: 30px;
    height: 20px;
    margin-left: 4px;
    background-color: var(--unity-colors-button-background);
    border-color: var(--unity-colors-button-border);
    border-radius: 4px;
    color: var(--unity-colors-button-text);
    font-size: 12px;
}

.refresh-models-button:hover {
    background-color: var(--unity-colors-button-background-hover);
}

.refresh-models-button:active {
    background-color: var(--unity-colors-button-background-pressed);
}

/* Template Controls */
.template-controls {
    flex-direction: row;
    align-items: center;
}

.template-dropdown {
    flex-grow: 1;
    margin-right: 8px;
    background-color: var(--unity-colors-dropdown-background);
    border-color: var(--unity-colors-dropdown-border);
    border-radius: 4px;
}

.template-button {
    width: 60px;
    margin-left: 4px;
    background-color: var(--unity-colors-button-background);
    border-color: var(--unity-colors-button-border);
    border-radius: 4px;
    color: var(--unity-colors-button-text);
}

.template-button:hover {
    background-color: var(--unity-colors-button-background-hover);
}

.template-button:active {
    background-color: var(--unity-colors-button-background-pressed);
}

/* Prompt Section */
.prompt-header {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.enhance-prompt-button {
    height: 24px;
    min-width: 120px;
    background-color: #9C27B0;
    border-color: #7B1FA2;
    border-radius: 4px;
    color: white;
    font-size: 11px;
    -unity-font-style: bold;
}

.enhance-prompt-button:hover {
    background-color: #7B1FA2;
}

.enhance-prompt-button:active {
    background-color: #6A1B9A;
}

.enhance-prompt-button:disabled {
    background-color: #666666;
    border-color: #555555;
    color: #999999;
}

/* Prompt Field */
.prompt-field {
    min-height: 120px;
    background-color: var(--unity-colors-input_field-background);
    border-color: var(--unity-colors-input_field-border);
    border-radius: 6px;
    padding: 8px;
    font-size: 13px;
    white-space: normal;
}

.prompt-field:focus {
    border-color: var(--unity-colors-input_field-border-focus);
}

/* Action Buttons */
.action-section {
    flex-direction: row;
    justify-content: center;
}

.action-button {
    height: 32px;
    min-width: 120px;
    margin: 0 8px;
    border-radius: 6px;
    font-size: 14px;
    -unity-font-style: bold;
}

.generate-button {
    background-color: #4CAF50;
    border-color: #45a049;
    color: white;
}

.generate-button:hover {
    background-color: #45a049;
}

.generate-button:active {
    background-color: #3d8b40;
}

.generate-button:disabled {
    background-color: #666666;
    border-color: #555555;
    color: #999999;
}

.apply-button {
    background-color: #2196F3;
    border-color: #1976D2;
    color: white;
}

.apply-button:hover {
    background-color: #1976D2;
}

.apply-button:active {
    background-color: #1565C0;
}

.apply-button:disabled {
    background-color: #666666;
    border-color: #555555;
    color: #999999;
}

/* Status Section */
.status-label {
    color: var(--unity-colors-default-text);
    margin-bottom: 4px;
    font-size: 13px;
}

.progress-bar {
    height: 4px;
    background-color: var(--unity-colors-slider_groove-background);
    border-radius: 2px;
    display: none;
}

/* Preview Section */
.preview-header {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.preview-toggle {
    margin: 0;
}

.code-preview-container {
    min-height: 200px;
    max-height: 400px;
    background-color: #1e1e1e;
    border-radius: 6px;
    border-width: 1px;
    border-color: var(--unity-colors-input_field-border);
    padding: 8px;
}

/* Code Preview Styling */
.code-block {
    margin-bottom: 16px;
}

.code-header {
    background-color: #2d2d30;
    padding: 8px 12px;
    border-radius: 4px 4px 0 0;
    border-bottom: 1px solid #3e3e42;
}

.code-title {
    color: #ffffff;
    font-size: 12px;
    -unity-font-style: bold;
}

.code-content {
    background-color: #1e1e1e;
    padding: 12px;
    border-radius: 0 0 4px 4px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 11px;
    white-space: pre;
    color: #d4d4d4;
}

/* Syntax Highlighting */
.syntax-tag {
    color: #569cd6;
}

.syntax-attribute {
    color: #9cdcfe;
}

.syntax-string {
    color: #ce9178;
}

.syntax-comment {
    color: #6a9955;
    -unity-font-style: italic;
}

.syntax-property {
    color: #9cdcfe;
}

.syntax-value {
    color: #ce9178;
}

.syntax-selector {
    color: #d7ba7d;
}

/* Responsive Design - Compact Mode */
.root-container {
    min-width: 400px;
    min-height: 500px;
}

/* Compact input rows for small windows */
.input-row {
    flex-direction: column;
    align-items: stretch;
    margin-bottom: 6px;
}

.input-label {
    width: auto;
    margin-bottom: 2px;
    margin-right: 0;
    font-size: 12px;
    -unity-font-style: bold;
}

.input-field {
    min-height: 18px;
}

/* Compact template controls */
.template-controls {
    flex-direction: column;
    align-items: stretch;
}

.template-dropdown {
    margin-right: 0;
    margin-bottom: 4px;
}

.template-button {
    width: auto;
    margin: 2px 0;
    height: 20px;
    font-size: 10px;
}

/* Compact prompt section */
.prompt-header {
    flex-direction: column;
    align-items: stretch;
    margin-bottom: 4px;
}

.enhance-prompt-button {
    height: 20px;
    margin-top: 4px;
    font-size: 10px;
}

.prompt-field {
    min-height: 80px;
    font-size: 12px;
}

/* Compact action buttons */
.action-section {
    flex-direction: column;
    align-items: stretch;
}

.action-button {
    margin: 2px 0;
    height: 28px;
    font-size: 12px;
}

/* Compact sections */
.settings-section,
.template-section,
.prompt-section,
.action-section,
.status-section,
.preview-section {
    margin-bottom: 8px;
    padding: 8px;
}

.section-title {
    font-size: 14px;
    margin-bottom: 4px;
}

/* Compact header */
.header-section {
    margin-bottom: 10px;
    padding: 8px;
}

.header-title {
    font-size: 18px;
    margin-bottom: 2px;
}

.header-subtitle {
    font-size: 12px;
}

/* Compact code preview */
.code-preview-container {
    min-height: 120px;
    max-height: 200px;
}

/* Compact refresh button */
.refresh-models-button {
    width: 25px;
    height: 18px;
    font-size: 10px;
}

/* Status section compact */
.status-label {
    font-size: 11px;
}

.progress-bar {
    height: 3px;
}

/* Compact Mode Specific Styles */
.compact-mode .header-section {
    margin-bottom: 6px;
    padding: 6px;
}

.compact-mode .header-title {
    font-size: 16px;
    margin-bottom: 0;
}

.compact-mode .settings-section,
.compact-mode .template-section,
.compact-mode .prompt-section,
.compact-mode .action-section,
.compact-mode .status-section,
.compact-mode .preview-section {
    margin-bottom: 6px;
    padding: 6px;
}

.compact-mode .section-title {
    font-size: 13px;
    margin-bottom: 3px;
}

.compact-mode .input-row {
    margin-bottom: 4px;
}

.compact-mode .input-label {
    font-size: 11px;
    margin-bottom: 1px;
}

.compact-mode .input-field {
    min-height: 16px;
    font-size: 11px;
}

.compact-mode .template-button {
    height: 18px;
    font-size: 9px;
    margin: 1px 0;
}

.compact-mode .enhance-prompt-button {
    height: 18px;
    font-size: 9px;
    margin-top: 2px;
}

.compact-mode .action-button {
    height: 24px;
    font-size: 11px;
    margin: 1px 0;
}

.compact-mode .refresh-models-button {
    width: 22px;
    height: 16px;
    font-size: 9px;
}

.compact-mode .code-preview-container {
    min-height: 100px;
    max-height: 150px;
}

.compact-mode .status-label {
    font-size: 10px;
}

.compact-mode .progress-bar {
    height: 2px;
}

/* Drag & Drop Section */
.dragdrop-section {
    margin-bottom: 16px;
    padding: 12px;
    background-color: var(--unity-colors-inspector_titlebar-background);
    border-radius: 6px;
    border-width: 1px;
    border-color: var(--unity-colors-inspector_titlebar-border);
}

.drop-zone {
    min-height: 60px;
    background-color: var(--unity-colors-input_field-background);
    border-width: 2px;
    border-color: var(--unity-colors-input_field-border);
    border-radius: 8px;
    border-style: dashed;
    justify-content: center;
    align-items: center;
    padding: 16px;
    margin-bottom: 8px;
    transition-property: border-color, background-color;
    transition-duration: 0.2s;
}

.drop-zone-hover {
    border-color: #4CAF50;
    background-color: rgba(76, 175, 80, 0.1);
}

.drop-zone-accept {
    border-color: #4CAF50;
    background-color: rgba(76, 175, 80, 0.2);
}

.drop-zone-reject {
    border-color: #f44336;
    background-color: rgba(244, 67, 54, 0.1);
}

.drop-zone-label {
    font-size: 14px;
    color: var(--unity-colors-default-text);
    -unity-font-style: bold;
    margin-bottom: 4px;
}

.drop-zone-hint {
    font-size: 12px;
    color: var(--unity-colors-default-text_75);
    -unity-font-style: italic;
}

.loaded-ui-info {
    background-color: var(--unity-colors-input_field-background);
    border-radius: 4px;
    padding: 8px;
    border-width: 1px;
    border-color: var(--unity-colors-input_field-border);
}

.loaded-ui-label {
    font-size: 12px;
    color: var(--unity-colors-default-text);
    margin-bottom: 8px;
}

.loaded-ui-actions {
    flex-direction: row;
    justify-content: space-between;
}

.loaded-ui-button {
    flex-grow: 1;
    height: 24px;
    margin: 0 2px;
    font-size: 11px;
    background-color: var(--unity-colors-button-background);
    border-color: var(--unity-colors-button-border);
    border-radius: 4px;
    color: var(--unity-colors-button-text);
}

.loaded-ui-button:hover {
    background-color: var(--unity-colors-button-background-hover);
}

/* File Naming Section */
.file-naming-section {
    margin-bottom: 16px;
    padding: 12px;
    background-color: #2d2d30;
    border-radius: 6px;
    border-width: 1px;
    border-color: #3e3e42;
}

.file-naming-content {
    flex-direction: column;
}

.suggested-name-container,
.custom-name-container {
    flex-direction: row;
    align-items: center;
    margin-bottom: 8px;
}

.suggested-name-label,
.custom-name-label {
    width: 80px;
    min-width: 80px;
    font-size: 12px;
    color: var(--unity-colors-default-text);
    margin-right: 8px;
}

.suggested-name-value {
    flex-grow: 1;
    font-size: 13px;
    color: #4CAF50;
    -unity-font-style: bold;
    background-color: rgba(76, 175, 80, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    margin-right: 8px;
}

.generate-name-button {
    width: 120px;
    height: 24px;
    font-size: 10px;
    background-color: #FF9800;
    border-color: #F57C00;
    border-radius: 4px;
    color: white;
    -unity-font-style: bold;
}

.generate-name-button:hover {
    background-color: #F57C00;
}

.custom-name-field {
    flex-grow: 1;
    height: 24px;
    background-color: var(--unity-colors-input_field-background);
    border-color: var(--unity-colors-input_field-border);
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
}

.file-naming-actions {
    flex-direction: row;
    justify-content: center;
    margin-top: 8px;
}

.file-naming-button {
    width: 100px;
    height: 28px;
    margin: 0 4px;
    font-size: 11px;
    border-radius: 4px;
    -unity-font-style: bold;
}

.file-naming-button:nth-child(1) {
    background-color: #4CAF50;
    border-color: #45a049;
    color: white;
}

.file-naming-button:nth-child(1):hover {
    background-color: #45a049;
}

.file-naming-button:nth-child(2) {
    background-color: #2196F3;
    border-color: #1976D2;
    color: white;
}

.file-naming-button:nth-child(2):hover {
    background-color: #1976D2;
}

.file-naming-button:nth-child(3) {
    background-color: #666666;
    border-color: #555555;
    color: white;
}

.file-naming-button:nth-child(3):hover {
    background-color: #555555;
}

/* Animations */
.generate-button {
    transition-property: background-color;
    transition-duration: 0.2s;
}

.apply-button {
    transition-property: background-color;
    transition-duration: 0.2s;
}

.template-button {
    transition-property: background-color;
    transition-duration: 0.2s;
}

/* Utility Classes */
.hidden {
    display: none;
}

.flex-row {
    flex-direction: row;
}

.flex-column {
    flex-direction: column;
}

.flex-grow {
    flex-grow: 1;
}

.text-center {
    -unity-text-align: middle-center;
}

.text-error {
    color: #f44336;
}

.text-success {
    color: #4caf50;
}

.text-warning {
    color: #ff9800;
}
