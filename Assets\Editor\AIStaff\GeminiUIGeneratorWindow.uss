/* Root Container */
.root-container {
    flex-grow: 1;
    padding: 16px;
    background-color: var(--unity-colors-window-background);
}

/* Header Section */
.header-section {
    margin-bottom: 20px;
    padding: 16px;
    background-color: var(--unity-colors-inspector_titlebar-background);
    border-radius: 8px;
    border-width: 1px;
    border-color: var(--unity-colors-inspector_titlebar-border);
}

.header-title {
    font-size: 24px;
    -unity-font-style: bold;
    color: var(--unity-colors-default-text);
    margin-bottom: 4px;
}

.header-subtitle {
    font-size: 14px;
    color: var(--unity-colors-default-text_75);
    -unity-font-style: italic;
}

/* Section Styling */
.settings-section,
.template-section,
.prompt-section,
.action-section,
.status-section,
.preview-section {
    margin-bottom: 16px;
    padding: 12px;
    background-color: var(--unity-colors-inspector_titlebar-background);
    border-radius: 6px;
    border-width: 1px;
    border-color: var(--unity-colors-inspector_titlebar-border);
}

.section-title {
    font-size: 16px;
    -unity-font-style: bold;
    color: var(--unity-colors-default-text);
    margin-bottom: 8px;
}

/* Input Rows */
.input-row {
    flex-direction: row;
    align-items: center;
    margin-bottom: 8px;
}

.input-label {
    width: 80px;
    min-width: 80px;
    color: var(--unity-colors-default-text);
    margin-right: 8px;
}

.input-field {
    flex-grow: 1;
    min-height: 20px;
}

.api-key-field {
    background-color: var(--unity-colors-input_field-background);
    border-color: var(--unity-colors-input_field-border);
    border-radius: 4px;
}

.model-dropdown {
    background-color: var(--unity-colors-dropdown-background);
    border-color: var(--unity-colors-dropdown-border);
    border-radius: 4px;
}

/* Template Controls */
.template-controls {
    flex-direction: row;
    align-items: center;
}

.template-dropdown {
    flex-grow: 1;
    margin-right: 8px;
    background-color: var(--unity-colors-dropdown-background);
    border-color: var(--unity-colors-dropdown-border);
    border-radius: 4px;
}

.template-button {
    width: 60px;
    margin-left: 4px;
    background-color: var(--unity-colors-button-background);
    border-color: var(--unity-colors-button-border);
    border-radius: 4px;
    color: var(--unity-colors-button-text);
}

.template-button:hover {
    background-color: var(--unity-colors-button-background-hover);
}

.template-button:active {
    background-color: var(--unity-colors-button-background-pressed);
}

/* Prompt Field */
.prompt-field {
    min-height: 120px;
    background-color: var(--unity-colors-input_field-background);
    border-color: var(--unity-colors-input_field-border);
    border-radius: 6px;
    padding: 8px;
    font-size: 13px;
    white-space: normal;
}

.prompt-field:focus {
    border-color: var(--unity-colors-input_field-border-focus);
}

/* Action Buttons */
.action-section {
    flex-direction: row;
    justify-content: center;
}

.action-button {
    height: 32px;
    min-width: 120px;
    margin: 0 8px;
    border-radius: 6px;
    font-size: 14px;
    -unity-font-style: bold;
}

.generate-button {
    background-color: #4CAF50;
    border-color: #45a049;
    color: white;
}

.generate-button:hover {
    background-color: #45a049;
}

.generate-button:active {
    background-color: #3d8b40;
}

.generate-button:disabled {
    background-color: #666666;
    border-color: #555555;
    color: #999999;
}

.apply-button {
    background-color: #2196F3;
    border-color: #1976D2;
    color: white;
}

.apply-button:hover {
    background-color: #1976D2;
}

.apply-button:active {
    background-color: #1565C0;
}

.apply-button:disabled {
    background-color: #666666;
    border-color: #555555;
    color: #999999;
}

/* Status Section */
.status-label {
    color: var(--unity-colors-default-text);
    margin-bottom: 4px;
    font-size: 13px;
}

.progress-bar {
    height: 4px;
    background-color: var(--unity-colors-slider_groove-background);
    border-radius: 2px;
    display: none;
}

/* Preview Section */
.preview-header {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.preview-toggle {
    margin: 0;
}

.code-preview-container {
    min-height: 200px;
    max-height: 400px;
    background-color: #1e1e1e;
    border-radius: 6px;
    border-width: 1px;
    border-color: var(--unity-colors-input_field-border);
    padding: 8px;
}

/* Code Preview Styling */
.code-block {
    margin-bottom: 16px;
}

.code-header {
    background-color: #2d2d30;
    padding: 8px 12px;
    border-radius: 4px 4px 0 0;
    border-bottom: 1px solid #3e3e42;
}

.code-title {
    color: #ffffff;
    font-size: 12px;
    -unity-font-style: bold;
}

.code-content {
    background-color: #1e1e1e;
    padding: 12px;
    border-radius: 0 0 4px 4px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 11px;
    white-space: pre;
    color: #d4d4d4;
}

/* Syntax Highlighting */
.syntax-tag {
    color: #569cd6;
}

.syntax-attribute {
    color: #9cdcfe;
}

.syntax-string {
    color: #ce9178;
}

.syntax-comment {
    color: #6a9955;
    -unity-font-style: italic;
}

.syntax-property {
    color: #9cdcfe;
}

.syntax-value {
    color: #ce9178;
}

.syntax-selector {
    color: #d7ba7d;
}

/* Responsive Design */
@media (max-width: 600px) {
    .input-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .input-label {
        width: auto;
        margin-bottom: 4px;
        margin-right: 0;
    }
    
    .template-controls {
        flex-direction: column;
    }
    
    .template-dropdown {
        margin-right: 0;
        margin-bottom: 8px;
    }
    
    .action-section {
        flex-direction: column;
    }
    
    .action-button {
        margin: 4px 0;
    }
}

/* Animations */
.generate-button {
    transition-property: background-color;
    transition-duration: 0.2s;
}

.apply-button {
    transition-property: background-color;
    transition-duration: 0.2s;
}

.template-button {
    transition-property: background-color;
    transition-duration: 0.2s;
}

/* Utility Classes */
.hidden {
    display: none;
}

.flex-row {
    flex-direction: row;
}

.flex-column {
    flex-direction: column;
}

.flex-grow {
    flex-grow: 1;
}

.text-center {
    -unity-text-align: middle-center;
}

.text-error {
    color: #f44336;
}

.text-success {
    color: #4caf50;
}

.text-warning {
    color: #ff9800;
}
