Using pre-set license
Built from '6000.2/respin/6000.2.0b1-1618a92a88ac' branch; Version is '6000.2.0b1 (d17678da8412) revision 13727352'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'tr' Physical Memory: 65230 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-05-27T15:53:11Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.2.0b1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
C:/Users/<USER>/Animerge
-logFile
Logs/AssetImportWorker2.log
-srvPort
55674
-job-worker-count
11
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Users/<USER>/Animerge
C:/Users/<USER>/Animerge
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [58456]  Target information:

Player connection [58456]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1209154767 [EditorId] 1209154767 [Version] 1048832 [Id] WindowsEditor(7,EvPC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [58456]  * "[IP] ********** [Port] 0 [Flags] 2 [Guid] 1209154767 [EditorId] 1209154767 [Version] 1048832 [Id] WindowsEditor(7,EvPC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [58456] Host joined multi-casting on [***********:54997]...
Player connection [58456] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 11
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 217.06 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.2.0b1 (d17678da8412)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Animerge/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4090 (ID=0x2684)
    Vendor:   NVIDIA
    VRAM:     24142 MB
    Driver:   32.0.15.7640
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56512
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/PlaybackEngines/MetroSupport/UnityEditor.UWP.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.002277 seconds.
- Loaded All Assemblies, in  0.393 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 582 ms
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.914 seconds
Domain Reload Profiling: 1305ms
	BeginReloadAssembly (105ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (207ms)
		LoadAssemblies (103ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (204ms)
			TypeCache.Refresh (203ms)
				TypeCache.ScanAssembly (193ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (915ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (878ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (696ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (49ms)
			ProcessInitializeOnLoadAttributes (93ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.870 seconds
Refreshing native plugins compatible for Editor in 2.73 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000020238bec21e (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000020238bebe33 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000020238bebb0b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000020238beb02e (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000020238beacca (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000020238be747b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x0000020238be6cfb (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x0000020238be6f05 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x0000020237a38582 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x0000020237a3845b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x0000020237a2eab3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000020225fc53fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07bae0 (Unity) <lambda_dfe2fdae22f1629f164682966ac9a93e>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b45e (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000020238bec21e (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000020238bebe33 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000020238bebb0b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000020238beb02e (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000020238beacca (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000020237a2eccb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000020225fc53fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07bae0 (Unity) <lambda_dfe2fdae22f1629f164682966ac9a93e>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b45e (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.121 seconds
Domain Reload Profiling: 1987ms
	BeginReloadAssembly (130ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (673ms)
		LoadAssemblies (413ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (331ms)
			TypeCache.Refresh (265ms)
				TypeCache.ScanAssembly (238ms)
			BuildScriptInfoCaches (54ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (1121ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (994ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (180ms)
			ProcessInitializeOnLoadAttributes (587ms)
			ProcessInitializeOnLoadMethodAttributes (201ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 2.32 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 289 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8286 unused Assets / (14.5 MB). Loaded Objects now: 10884.
Memory consumption went from 207.7 MB to 193.2 MB.
Total: 18.056000 ms (FindLiveObjects: 1.174400 ms CreateObjectMapping: 1.238500 ms MarkObjects: 7.189000 ms  DeleteObjects: 8.452800 ms)

