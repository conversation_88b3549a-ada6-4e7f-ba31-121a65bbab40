<ui:UXML xmlns:ui="UnityEngine.UIElements">
    <Style src="project://database/Assets/UI Toolkit/Generated\GeneratedUI_20250527_200505.uss" />
    <ui:TwoPaneSplitView class="dashboard-layout" fixed-pane-initial-dimension="200" orientation="Horizontal">
        <!-- Sidebar -->
        <ui:VisualElement class="sidebar">
            <ui:Label text="Dashboard" class="sidebar-title" />
            <ui:VisualElement class="sidebar-navigation">
                <ui:Button text="Overview" class="sidebar-button" />
                <ui:Button text="Analytics" class="sidebar-button" />
                <ui:Button text="Reports" class="sidebar-button" />
                <ui:Button text="Settings" class="sidebar-button" />
            </ui:VisualElement>
        </ui:VisualElement>

        <!-- Main Content Area -->
        <ui:ScrollView class="dashboard-content-scrollview">
            <ui:VisualElement class="dashboard-content">
                <ui:Label text="Dashboard Overview" class="dashboard-title" />

                <!-- Cards Container -->
                <ui:VisualElement class="card-container">
                    <!-- Card 1: Sales Summary -->
                    <ui:Box class="statistic-card">
                        <ui:Label text="Total Sales" class="card-header" />
                        <ui:Label text="$12,345" class="card-value" />
                        <ui:VisualElement class="chart-placeholder" />
                    </ui:Box>

                    <!-- Card 2: New Users -->
                    <ui:Box class="statistic-card">
                        <ui:Label text="New Users" class="card-header" />
                        <ui:Label text="1,234" class="card-value" />
                        <ui:VisualElement class="chart-placeholder" />
                    </ui:Box>

                    <!-- Card 3: Website Traffic -->
                    <ui:Box class="statistic-card">
                        <ui:Label text="Website Traffic" class="card-header" />
                        <ui:Label text="56,789" class="card-value" />
                        <ui:VisualElement class="chart-placeholder" />
                    </ui:Box>

                    <!-- Card 4: Conversion Rate -->
                    <ui:Box class="statistic-card">
                        <ui:Label text="Conversion Rate" class="card-header" />
                        <ui:Label text="4.5%" class="card-value" />
                        <ui:VisualElement class="chart-placeholder" />
                    </ui:Box>
                </ui:VisualElement>

                <!-- Metrics Section -->
                <ui:Label text="Key Metrics" class="section-title" />
                <ui:VisualElement class="metrics-section">
                    <ui:VisualElement class="metric-item">
                        <ui:Label text="Project Progress" class="metric-label" />
                        <ui:ProgressBar low-value="0" high-value="100" value="75" class="metric-progress-bar" />
                    </ui:VisualElement>

                    <ui:VisualElement class="metric-item">
                        <ui:Label text="Task Completion" class="metric-label" />
                        <ui:ProgressBar low-value="0" high-value="100" value="90" class="metric-progress-bar" />
                    </ui:VisualElement>

                    <ui:VisualElement class="metric-item">
                        <ui:Label text="Resource Utilization" class="metric-label" />
                        <ui:ProgressBar low-value="0" high-value="100" value="60" class="metric-progress-bar" />
                    </ui:VisualElement>
                </ui:VisualElement>

            </ui:VisualElement>
        </ui:ScrollView>
    </ui:TwoPaneSplitView>
</ui:UXML>