using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Networking;

namespace AIStaff.UIGenerator
{
    [System.Serializable]
    public class GeminiResponse
    {
        public bool success;
        public string uxmlCode;
        public string ussCode;
        public string error;
        public string rawResponse;
    }

    [System.Serializable]
    public class GeminiAPIRequest
    {
        public List<Content> contents;
        public GenerationConfig generationConfig;
        public List<SafetySetting> safetySettings;
    }

    [System.Serializable]
    public class Content
    {
        public List<Part> parts;
    }

    [System.Serializable]
    public class Part
    {
        public string text;
    }

    [System.Serializable]
    public class GenerationConfig
    {
        public float temperature;
        public int topK;
        public float topP;
        public int maxOutputTokens;
        public List<string> stopSequences;
    }

    [System.Serializable]
    public class SafetySetting
    {
        public string category;
        public string threshold;
    }

    [System.Serializable]
    public class GeminiAPIResponse
    {
        public List<Candidate> candidates;
        public PromptFeedback promptFeedback;
    }

    [System.Serializable]
    public class Candidate
    {
        public Content content;
        public string finishReason;
        public int index;
        public List<SafetyRating> safetyRatings;
    }

    [System.Serializable]
    public class PromptFeedback
    {
        public List<SafetyRating> safetyRatings;
    }

    [System.Serializable]
    public class SafetyRating
    {
        public string category;
        public string probability;
    }

    public class GeminiAPIClient
    {
        private const string GEMINI_API_BASE_URL = "https://generativelanguage.googleapis.com/v1beta/models/";
        private const int REQUEST_TIMEOUT = 60;

        public async Task<GeminiResponse> GenerateUICode(string prompt, string apiKey, string model)
        {
            try
            {
                string enhancedPrompt = CreateEnhancedPrompt(prompt);
                string requestBody = CreateRequestBody(enhancedPrompt);
                string url = $"{GEMINI_API_BASE_URL}{model}:generateContent?key={apiKey}";

                using (UnityWebRequest request = new UnityWebRequest(url, "POST"))
                {
                    byte[] bodyRaw = Encoding.UTF8.GetBytes(requestBody);
                    request.uploadHandler = new UploadHandlerRaw(bodyRaw);
                    request.downloadHandler = new DownloadHandlerBuffer();
                    request.SetRequestHeader("Content-Type", "application/json");
                    request.timeout = REQUEST_TIMEOUT;

                    var operation = request.SendWebRequest();

                    // Wait for completion
                    while (!operation.isDone)
                    {
                        await Task.Delay(100);
                    }

                    if (request.result == UnityWebRequest.Result.Success)
                    {
                        string responseText = request.downloadHandler.text;
                        return ParseResponse(responseText);
                    }
                    else
                    {
                        string errorMessage = $"HTTP Error {request.responseCode}: {request.error}";
                        if (!string.IsNullOrEmpty(request.downloadHandler.text))
                        {
                            errorMessage += $"\nResponse: {request.downloadHandler.text}";
                        }

                        return new GeminiResponse
                        {
                            success = false,
                            error = errorMessage
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                return new GeminiResponse
                {
                    success = false,
                    error = $"Exception: {ex.Message}"
                };
            }
        }

        private string CreateEnhancedPrompt(string userPrompt)
        {
            return $@"You are an expert Unity UI Toolkit developer. Generate UXML and USS code for the following UI description:

{userPrompt}

CRITICAL Unity UI Toolkit Requirements:
1. Generate valid UXML (Unity XML) code with SINGLE ROOT ELEMENT only
2. UXML must start with <ui:UXML> and end with </ui:UXML>
3. UXML must have proper Unity namespaces: xmlns:ui=""UnityEngine.UIElements""
4. Use ONLY these Unity UI Toolkit elements: VisualElement, Label, Button, TextField, ScrollView, Foldout, Toggle, Slider, ProgressBar, Image, DropdownField, IntegerField, FloatField, Vector2Field, Vector3Field, ColorField, Box, TwoPaneSplitView
5. DO NOT use these elements: Panel, Container, Div, Span, ListView, TreeView, Canvas, Form, Input, Select, Toolbar elements, HelpBox, Space, FlexibleSpace
6. Follow Unity naming conventions (PascalCase for element names)
7. Include proper class names for USS styling

USS (Unity Style Sheet) Limitations:
- DO NOT use :hover, :active, :focus pseudo-classes (NOT SUPPORTED in Unity)
- DO NOT use CSS animations or transitions (NOT SUPPORTED)
- Use Unity-specific properties: -unity-font-style, -unity-text-align, etc.
- For colors: Use hex colors (#FF0000), Unity color variables (--unity-colors-default-text), or simple color names (red, blue, white, black)
- DO NOT use CSS color functions: rgb(), rgba(), hsl(), var() (NOT SUPPORTED)
- Use Unity measurement units: px, %, or no unit for Unity-specific properties

Valid Unity USS Properties:
- background-color, color, font-size, margin, padding, border-width, border-color
- flex-direction, justify-content, align-items, flex-grow, flex-shrink
- width, height, min-width, min-height, max-width, max-height
- position, top, left, right, bottom
- -unity-font-style, -unity-text-align, -unity-background-scale-mode

Output format (EXACTLY like this):
```uxml
<ui:UXML xmlns:ui=""UnityEngine.UIElements"">
    [Your UXML content here - SINGLE ROOT ELEMENT]
</ui:UXML>
```

```uss
[Your USS content here - NO :hover, :active, :focus]
```

CRITICAL RULES:
- UXML must have exactly ONE root element inside <ui:UXML>
- NO :hover, :active, :focus in USS
- NO CSS animations or transitions
- Use only Unity-supported properties
- Test your output for Unity compatibility";
        }

        private string CreateRequestBody(string prompt)
        {
            var request = new GeminiAPIRequest
            {
                contents = new List<Content>
                {
                    new Content
                    {
                        parts = new List<Part>
                        {
                            new Part { text = prompt }
                        }
                    }
                },
                generationConfig = new GenerationConfig
                {
                    temperature = 0.7f,
                    topK = 40,
                    topP = 0.95f,
                    maxOutputTokens = 8192,
                    stopSequences = new List<string>()
                },
                safetySettings = new List<SafetySetting>
                {
                    new SafetySetting { category = "HARM_CATEGORY_HARASSMENT", threshold = "BLOCK_MEDIUM_AND_ABOVE" },
                    new SafetySetting { category = "HARM_CATEGORY_HATE_SPEECH", threshold = "BLOCK_MEDIUM_AND_ABOVE" },
                    new SafetySetting { category = "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold = "BLOCK_MEDIUM_AND_ABOVE" },
                    new SafetySetting { category = "HARM_CATEGORY_DANGEROUS_CONTENT", threshold = "BLOCK_MEDIUM_AND_ABOVE" }
                }
            };

            return JsonUtility.ToJson(request);
        }

        private GeminiResponse ParseResponse(string responseText)
        {
            try
            {
                var apiResponse = JsonUtility.FromJson<GeminiAPIResponse>(responseText);

                if (apiResponse?.candidates == null || apiResponse.candidates.Count == 0)
                {
                    return new GeminiResponse
                    {
                        success = false,
                        error = "No candidates in response",
                        rawResponse = responseText
                    };
                }

                var candidate = apiResponse.candidates[0];
                if (candidate?.content?.parts == null || candidate.content.parts.Count == 0)
                {
                    return new GeminiResponse
                    {
                        success = false,
                        error = "No content in candidate",
                        rawResponse = responseText
                    };
                }

                string generatedText = candidate.content.parts[0].text;
                return ExtractCodeBlocks(generatedText, responseText);
            }
            catch (Exception ex)
            {
                return new GeminiResponse
                {
                    success = false,
                    error = $"Failed to parse response: {ex.Message}",
                    rawResponse = responseText
                };
            }
        }

        private GeminiResponse ExtractCodeBlocks(string text, string rawResponse)
        {
            try
            {
                string uxmlCode = "";
                string ussCode = "";

                // Extract UXML code block
                int uxmlStart = text.IndexOf("```uxml");
                if (uxmlStart != -1)
                {
                    uxmlStart += 7; // Skip "```uxml"
                    int uxmlEnd = text.IndexOf("```", uxmlStart);
                    if (uxmlEnd != -1)
                    {
                        uxmlCode = text.Substring(uxmlStart, uxmlEnd - uxmlStart).Trim();
                    }
                }

                // Extract USS code block
                int ussStart = text.IndexOf("```uss");
                if (ussStart != -1)
                {
                    ussStart += 6; // Skip "```uss"
                    int ussEnd = text.IndexOf("```", ussStart);
                    if (ussEnd != -1)
                    {
                        ussCode = text.Substring(ussStart, ussEnd - ussStart).Trim();
                    }
                }

                // Fallback: try to extract any code blocks
                if (string.IsNullOrEmpty(uxmlCode) && string.IsNullOrEmpty(ussCode))
                {
                    var codeBlocks = ExtractAllCodeBlocks(text);
                    if (codeBlocks.Count > 0)
                    {
                        // Assume first block is UXML, second is USS
                        uxmlCode = codeBlocks[0];
                        if (codeBlocks.Count > 1)
                            ussCode = codeBlocks[1];
                    }
                }

                if (string.IsNullOrEmpty(uxmlCode))
                {
                    return new GeminiResponse
                    {
                        success = false,
                        error = "No UXML code found in response",
                        rawResponse = rawResponse
                    };
                }

                return new GeminiResponse
                {
                    success = true,
                    uxmlCode = uxmlCode,
                    ussCode = ussCode,
                    rawResponse = rawResponse
                };
            }
            catch (Exception ex)
            {
                return new GeminiResponse
                {
                    success = false,
                    error = $"Failed to extract code blocks: {ex.Message}",
                    rawResponse = rawResponse
                };
            }
        }

        private List<string> ExtractAllCodeBlocks(string text)
        {
            var codeBlocks = new List<string>();
            int startIndex = 0;

            while (true)
            {
                int codeStart = text.IndexOf("```", startIndex);
                if (codeStart == -1) break;

                // Skip the opening ```
                int contentStart = text.IndexOf('\n', codeStart);
                if (contentStart == -1) break;
                contentStart++;

                int codeEnd = text.IndexOf("```", contentStart);
                if (codeEnd == -1) break;

                string codeBlock = text.Substring(contentStart, codeEnd - contentStart).Trim();
                if (!string.IsNullOrEmpty(codeBlock))
                {
                    codeBlocks.Add(codeBlock);
                }

                startIndex = codeEnd + 3;
            }

            return codeBlocks;
        }

        public bool ValidateAPIKey(string apiKey)
        {
            return !string.IsNullOrEmpty(apiKey) && apiKey.Length > 10;
        }

        public async Task<List<string>> GetAvailableModelsFromAPI(string apiKey)
        {
            try
            {
                string url = $"https://generativelanguage.googleapis.com/v1beta/models?key={apiKey}";

                using (UnityWebRequest request = UnityWebRequest.Get(url))
                {
                    request.timeout = 30;

                    var operation = request.SendWebRequest();

                    // Wait for completion
                    while (!operation.isDone)
                    {
                        await Task.Delay(100);
                    }

                    if (request.result == UnityWebRequest.Result.Success)
                    {
                        string responseText = request.downloadHandler.text;
                        return ParseModelsResponse(responseText);
                    }
                    else
                    {
                        Debug.LogWarning($"Failed to fetch models from API: {request.error}");
                        return GetDefaultModels();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Exception while fetching models: {ex.Message}");
                return GetDefaultModels();
            }
        }

        public List<string> GetDefaultModels()
        {
            return new List<string>
            {
                "gemini-1.5-pro",
                "gemini-1.5-flash",
                "gemini-1.0-pro"
            };
        }

        private List<string> ParseModelsResponse(string responseText)
        {
            try
            {
                var models = new List<string>();

                // Simple JSON parsing for models
                var lines = responseText.Split('\n');
                foreach (var line in lines)
                {
                    if (line.Contains("\"name\":") && line.Contains("models/gemini"))
                    {
                        // Extract model name from: "name": "models/gemini-1.5-pro"
                        int startIndex = line.IndexOf("models/") + 7;
                        int endIndex = line.IndexOf("\"", startIndex);

                        if (startIndex > 6 && endIndex > startIndex)
                        {
                            string modelName = line.Substring(startIndex, endIndex - startIndex);
                            if (!models.Contains(modelName) && modelName.StartsWith("gemini"))
                            {
                                models.Add(modelName);
                            }
                        }
                    }
                }

                // Sort models by version (newer first)
                models.Sort((a, b) => b.CompareTo(a));

                return models.Count > 0 ? models : GetDefaultModels();
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Failed to parse models response: {ex.Message}");
                return GetDefaultModels();
            }
        }

        public async Task<GeminiResponse> EnhancePrompt(string originalPrompt, string apiKey, string model)
        {
            try
            {
                string enhancePrompt = CreatePromptEnhancementPrompt(originalPrompt);
                string requestBody = CreateRequestBody(enhancePrompt);
                string url = $"{GEMINI_API_BASE_URL}{model}:generateContent?key={apiKey}";

                using (UnityWebRequest request = new UnityWebRequest(url, "POST"))
                {
                    byte[] bodyRaw = Encoding.UTF8.GetBytes(requestBody);
                    request.uploadHandler = new UploadHandlerRaw(bodyRaw);
                    request.downloadHandler = new DownloadHandlerBuffer();
                    request.SetRequestHeader("Content-Type", "application/json");
                    request.timeout = REQUEST_TIMEOUT;

                    var operation = request.SendWebRequest();

                    // Wait for completion
                    while (!operation.isDone)
                    {
                        await Task.Delay(100);
                    }

                    if (request.result == UnityWebRequest.Result.Success)
                    {
                        string responseText = request.downloadHandler.text;
                        return ParseEnhancePromptResponse(responseText);
                    }
                    else
                    {
                        string errorMessage = $"HTTP Error {request.responseCode}: {request.error}";
                        return new GeminiResponse
                        {
                            success = false,
                            error = errorMessage
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                return new GeminiResponse
                {
                    success = false,
                    error = $"Exception: {ex.Message}"
                };
            }
        }

        private string CreatePromptEnhancementPrompt(string originalPrompt)
        {
            return $@"You are an expert Unity UI Toolkit designer. Enhance and improve the following UI description to be more detailed, specific, and suitable for Unity UI Toolkit development.

Original prompt: {originalPrompt}

Please enhance this prompt by:
1. Adding specific Unity UI Toolkit element suggestions
2. Including layout and styling details
3. Specifying colors, spacing, and visual hierarchy
4. Adding interaction and usability considerations
5. Making it more detailed and actionable

Enhanced prompt should be 2-3 times more detailed than the original while staying focused on Unity UI Toolkit capabilities.

IMPORTANT: Only return the enhanced prompt text, no explanations or additional text.";
        }

        private GeminiResponse ParseEnhancePromptResponse(string responseText)
        {
            try
            {
                var apiResponse = JsonUtility.FromJson<GeminiAPIResponse>(responseText);

                if (apiResponse?.candidates == null || apiResponse.candidates.Count == 0)
                {
                    return new GeminiResponse
                    {
                        success = false,
                        error = "No candidates in response",
                        rawResponse = responseText
                    };
                }

                var candidate = apiResponse.candidates[0];
                if (candidate?.content?.parts == null || candidate.content.parts.Count == 0)
                {
                    return new GeminiResponse
                    {
                        success = false,
                        error = "No content in candidate",
                        rawResponse = responseText
                    };
                }

                string enhancedPrompt = candidate.content.parts[0].text.Trim();

                return new GeminiResponse
                {
                    success = true,
                    uxmlCode = enhancedPrompt, // We'll use uxmlCode field to store enhanced prompt
                    ussCode = "",
                    rawResponse = responseText
                };
            }
            catch (Exception ex)
            {
                return new GeminiResponse
                {
                    success = false,
                    error = $"Failed to parse enhance prompt response: {ex.Message}",
                    rawResponse = responseText
                };
            }
        }
    }
}
