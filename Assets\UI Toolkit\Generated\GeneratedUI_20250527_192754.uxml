<?xml version="1.0" encoding="utf-8"?>
    <Style src="project://database/Assets/UI Toolkit/Generated\GeneratedUI_20250527_192754.uss" />
<UXML xmlns:ui="UnityEngine.UIElements">
  <VisualElement name="InventoryPanel" class="inventory-panel">
    <TextField name="SearchBar" class="search-bar" multiline="false"/>
    <VisualElement name="CategoryTabs" class="category-tabs">
      <Button name="TabAll" class="category-tab">All</Button>
      <Button name="TabWeapons" class="category-tab">Weapons</Button>
      <Button name="TabArmor" class="category-tab">Armor</Button>
      <Button name="TabConsumables" class="category-tab">Consumables</Button>
    </VisualElement>
    <VisualElement name="InventoryGrid" class="inventory-grid">
      <RepeatButton name="SlotTemplate" class="inventory-slot">
        <VisualElement name="Icon" class="item-icon"/>
        <Label name="QuantityLabel" class="item-quantity"/>
      </RepeatButton>
    </VisualElement>
  </VisualElement>
</UXML>