Using pre-set license
Built from '6000.2/respin/6000.2.0b1-1618a92a88ac' branch; Version is '6000.2.0b1 (d17678da8412) revision 13727352'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'tr' Physical Memory: 65230 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-05-27T16:30:45Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.2.0b1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker5
-projectPath
C:/Users/<USER>/Animerge
-logFile
Logs/AssetImportWorker5.log
-srvPort
55674
-job-worker-count
11
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Users/<USER>/Animerge
C:/Users/<USER>/Animerge
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [117164]  Target information:

Player connection [117164]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1224705314 [EditorId] 1224705314 [Version] 1048832 [Id] WindowsEditor(7,EvPC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [117164]  * "[IP] ********** [Port] 0 [Flags] 2 [Guid] 1224705314 [EditorId] 1224705314 [Version] 1048832 [Id] WindowsEditor(7,EvPC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [117164] Host joined multi-casting on [***********:54997]...
Player connection [117164] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 11
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 3.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.2.0b1 (d17678da8412)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Animerge/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4090 (ID=0x2684)
    Vendor:   NVIDIA
    VRAM:     24142 MB
    Driver:   32.0.15.7640
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56848
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/PlaybackEngines/MetroSupport/UnityEditor.UWP.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.0b1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.002137 seconds.
- Loaded All Assemblies, in  0.313 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 250 ms
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.675 seconds
Domain Reload Profiling: 987ms
	BeginReloadAssembly (108ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (127ms)
		LoadAssemblies (107ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (124ms)
			TypeCache.Refresh (123ms)
				TypeCache.ScanAssembly (113ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (676ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (639ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (369ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (131ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.859 seconds
Refreshing native plugins compatible for Editor in 2.04 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000011f36f4c21e (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000011f36f4be33 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000011f36f4bb0b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000011f36f4b02e (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000011f36f4acca (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000011f36f4747b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x0000011f36f46cfb (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x0000011f36f46f05 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x0000011f35d98742 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x0000011f35d9861b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x0000011f35d8f433 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000011f243553fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07bae0 (Unity) <lambda_dfe2fdae22f1629f164682966ac9a93e>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b45e (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000011f36f4c21e (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000011f36f4be33 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000011f36f4bb0b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000011f36f4b02e (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000011f36f4acca (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000011f35d8f64b (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000011f243553fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07bae0 (Unity) <lambda_dfe2fdae22f1629f164682966ac9a93e>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b45e (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.029 seconds
Domain Reload Profiling: 1884ms
	BeginReloadAssembly (131ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (22ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (659ms)
		LoadAssemblies (428ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (303ms)
			TypeCache.Refresh (235ms)
				TypeCache.ScanAssembly (216ms)
			BuildScriptInfoCaches (57ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (1029ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (903ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (185ms)
			ProcessInitializeOnLoadAttributes (496ms)
			ProcessInitializeOnLoadMethodAttributes (194ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 2.18 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 289 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8286 unused Assets / (13.3 MB). Loaded Objects now: 10884.
Memory consumption went from 205.9 MB to 192.6 MB.
Total: 11.282700 ms (FindLiveObjects: 0.716600 ms CreateObjectMapping: 0.668500 ms MarkObjects: 4.813400 ms  DeleteObjects: 5.082800 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.877 seconds
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000011f36f4582e (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000011f36f45653 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000011f36f4532b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000011f36f4484e (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000011f36f444ea (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000011f36f40c9b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x0000011f36f4051b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x0000011f36f40725 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x0000011f2f505ac2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x0000011f2f50599b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x0000011f2f4fbac3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000011f408e53fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000011f36f4582e (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000011f36f45653 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000011f36f4532b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000011f36f4484e (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000011f36f444ea (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000011f2f4fbcdb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000011f408e53fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.793 seconds
Domain Reload Profiling: 1667ms
	BeginReloadAssembly (219ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (60ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (593ms)
		LoadAssemblies (442ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (236ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (212ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (793ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (639ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (193ms)
			ProcessInitializeOnLoadAttributes (325ms)
			ProcessInitializeOnLoadMethodAttributes (94ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 3.39 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8284 unused Assets / (11.7 MB). Loaded Objects now: 10887.
Memory consumption went from 177.1 MB to 165.4 MB.
Total: 10.423400 ms (FindLiveObjects: 0.652500 ms CreateObjectMapping: 0.636400 ms MarkObjects: 4.655100 ms  DeleteObjects: 4.478300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 32215.849681 seconds.
  path: Assets/Editor/AIStaff/PromptTemplateManager.cs
  artifactKey: Guid(b857154d44e2f7b438faa44d627b700f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Editor/AIStaff/PromptTemplateManager.cs using Guid(b857154d44e2f7b438faa44d627b700f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '85ca0b84983b944be30bdfb7f8d01621') in 0.1539005 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 34.999738 seconds.
  path: Assets/UI Toolkit/Generated/GeneratedUI_20250527_193529.uxml
  artifactKey: Guid(d498b4b7152540440ac10f212d390ef5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/UI Toolkit/Generated/GeneratedUI_20250527_193529.uxml using Guid(d498b4b7152540440ac10f212d390ef5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd0998d7ac907d570e69e73f20ffad8ae') in 0.0657566 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.476 seconds
Refreshing native plugins compatible for Editor in 4.87 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000011f36f4582e (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000011f36f45653 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000011f36f4532b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000011f36f4484e (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000011f36f444ea (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000011f36f40c9b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x0000011f36f4051b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x0000011f36f40725 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x0000011f24575ac2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x0000011f2457599b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x0000011f243abac3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000011f408553fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000011f36f4582e (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000011f36f45653 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000011f36f4532b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000011f36f4484e (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000011f36f444ea (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000011f243abcdb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000011f408553fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.421 seconds
Domain Reload Profiling: 2888ms
	BeginReloadAssembly (340ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (26ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (91ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (1022ms)
		LoadAssemblies (717ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (437ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (402ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1421ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1134ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (32ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (353ms)
			ProcessInitializeOnLoadAttributes (551ms)
			ProcessInitializeOnLoadMethodAttributes (180ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (30ms)
Refreshing native plugins compatible for Editor in 4.48 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8284 unused Assets / (12.7 MB). Loaded Objects now: 10891.
Memory consumption went from 177.8 MB to 165.1 MB.
Total: 15.700300 ms (FindLiveObjects: 1.217500 ms CreateObjectMapping: 0.663600 ms MarkObjects: 6.921300 ms  DeleteObjects: 6.896000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 761.279290 seconds.
  path: Assets/Editor/AIStaff/UICodeGenerator.cs
  artifactKey: Guid(88aab4f556fa52f42a3e8c37cf08c75e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Editor/AIStaff/UICodeGenerator.cs using Guid(88aab4f556fa52f42a3e8c37cf08c75e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1be4d070f51cc47fd4934f9868808cd1') in 0.2244839 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.798 seconds
Refreshing native plugins compatible for Editor in 2.39 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000011f36e14ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000011f36e14cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000011f36e149cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000011f36e13eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000011f36e13b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000011f36e1033b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x0000011f36dffa2b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x0000011f36dffc35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x0000011f24585ac2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x0000011f2458599b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x0000011f2457bac3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000011f408b53fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000011f36e14ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000011f36e14cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000011f36e149cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000011f36e13eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000011f36e13b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000011f2457bcdb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000011f408b53fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.794 seconds
Domain Reload Profiling: 1591ms
	BeginReloadAssembly (198ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (57ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (542ms)
		LoadAssemblies (390ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (225ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (203ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (795ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (640ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (186ms)
			ProcessInitializeOnLoadAttributes (320ms)
			ProcessInitializeOnLoadMethodAttributes (106ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 3.47 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8284 unused Assets / (11.9 MB). Loaded Objects now: 10893.
Memory consumption went from 177.8 MB to 165.9 MB.
Total: 13.573100 ms (FindLiveObjects: 1.291700 ms CreateObjectMapping: 0.945900 ms MarkObjects: 6.209300 ms  DeleteObjects: 5.124700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 47.145003 seconds.
  path: Assets/Editor/AIStaff/GeminiAPIClient.cs
  artifactKey: Guid(b4848c58dd15f25419dd3e1005372a70) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Editor/AIStaff/GeminiAPIClient.cs using Guid(b4848c58dd15f25419dd3e1005372a70) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dc33d04df903a2f253f7bdc98d1e36ff') in 0.1472402 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 42.308557 seconds.
  path: Assets/UI Toolkit/Generated/GeneratedUI_20250527_194941.uss
  artifactKey: Guid(f675922bf4ff5614d950d219316ab3e9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/UI Toolkit/Generated/GeneratedUI_20250527_194941.uss using Guid(f675922bf4ff5614d950d219316ab3e9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '258def953b9fdd34e28c2117b45e0ad1') in 0.0215485 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.861 seconds
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000011f36f44ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000011f36f44cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000011f36f449cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000011f36f43eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000011f36f43b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000011f36f4033b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x0000011f36f3fa2b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x0000011f36f3fc35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x0000011f42065ac2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x0000011f4206599b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x0000011f4205bac3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000011f41b753fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000011f36f44ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000011f36f44cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000011f36f449cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000011f36f43eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000011f36f43b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000011f4205bcdb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000011f41b753fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.830 seconds
Domain Reload Profiling: 1690ms
	BeginReloadAssembly (224ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (68ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (573ms)
		LoadAssemblies (417ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (236ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (213ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (830ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (672ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (197ms)
			ProcessInitializeOnLoadAttributes (336ms)
			ProcessInitializeOnLoadMethodAttributes (109ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 3.29 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8284 unused Assets / (13.5 MB). Loaded Objects now: 10895.
Memory consumption went from 177.8 MB to 164.3 MB.
Total: 15.815300 ms (FindLiveObjects: 1.140600 ms CreateObjectMapping: 1.410100 ms MarkObjects: 6.467500 ms  DeleteObjects: 6.795300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 339.221481 seconds.
  path: Assets/Editor/AIStaff/GeminiUIGeneratorWindow.cs
  artifactKey: Guid(5efcc33030f48024fb153f9a0cf878ad) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Editor/AIStaff/GeminiUIGeneratorWindow.cs using Guid(5efcc33030f48024fb153f9a0cf878ad) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2af0ade2bc59e3a8e135e2e020a9f3d0') in 0.1635632 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/Editor/AIStaff/GeminiUIGeneratorWindow.uxml
  artifactKey: Guid(db3e22cb339c6e748b39ac306fe68ddd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Editor/AIStaff/GeminiUIGeneratorWindow.uxml using Guid(db3e22cb339c6e748b39ac306fe68ddd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '71df0e963454418ea0e5c22e23cd03b0') in 0.0871489 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.862 seconds
Refreshing native plugins compatible for Editor in 2.29 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000011f36f44ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000011f36f44cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000011f36f449cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000011f36f43eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000011f36f43b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000011f36f4033b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x0000011f36f3fa2b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x0000011f36f3fc35 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x0000011f35d65ac2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x0000011f35d6599b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x0000011f35d5bac3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000011f22e553fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000011f36f44ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000011f36f44cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000011f36f449cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000011f36f43eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000011f36f43b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000011f35d5bcdb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000011f22e553fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.830 seconds
Domain Reload Profiling: 1691ms
	BeginReloadAssembly (225ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (67ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (574ms)
		LoadAssemblies (419ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (240ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (215ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (830ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (668ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (202ms)
			ProcessInitializeOnLoadAttributes (340ms)
			ProcessInitializeOnLoadMethodAttributes (97ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 4.20 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8284 unused Assets / (14.2 MB). Loaded Objects now: 10897.
Memory consumption went from 178.0 MB to 163.8 MB.
Total: 16.108800 ms (FindLiveObjects: 1.216300 ms CreateObjectMapping: 1.320700 ms MarkObjects: 5.886900 ms  DeleteObjects: 7.683200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 444.927212 seconds.
  path: Assets/Editor/AIStaff/UICodeGenerator.cs
  artifactKey: Guid(88aab4f556fa52f42a3e8c37cf08c75e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Editor/AIStaff/UICodeGenerator.cs using Guid(88aab4f556fa52f42a3e8c37cf08c75e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7cf1a7947ce50d8ed831262a3125270b') in 0.1404685 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 140.004474 seconds.
  path: Assets/UI Toolkit/Generated/GeneratedUI_20250527_200505.uss
  artifactKey: Guid(77105d4a936069a4983ecde7d3aaaceb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/UI Toolkit/Generated/GeneratedUI_20250527_200505.uss using Guid(77105d4a936069a4983ecde7d3aaaceb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b668305757bd54ec9f2ee67acc84f80e') in 0.0256484 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.839 seconds
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000011f36f44ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000011f36f44cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000011f36f449cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000011f36f43eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000011f36f43b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000011f36f4033b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x0000011f36f3f1cb (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x0000011f36f3f3d5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x0000011f42055ac2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x0000011f4205599b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x0000011f4204bac3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000011f41b753fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000011f36f44ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000011f36f44cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000011f36f449cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000011f36f43eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000011f36f43b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000011f4204bcdb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000011f41b753fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.818 seconds
Domain Reload Profiling: 1655ms
	BeginReloadAssembly (208ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (61ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (563ms)
		LoadAssemblies (404ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (235ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (214ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (818ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (656ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (193ms)
			ProcessInitializeOnLoadAttributes (329ms)
			ProcessInitializeOnLoadMethodAttributes (106ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 3.53 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8284 unused Assets / (12.1 MB). Loaded Objects now: 10899.
Memory consumption went from 178.0 MB to 165.9 MB.
Total: 11.274600 ms (FindLiveObjects: 0.722300 ms CreateObjectMapping: 0.717500 ms MarkObjects: 4.434800 ms  DeleteObjects: 5.398800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.93 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8261 unused Assets / (14.0 MB). Loaded Objects now: 10899.
Memory consumption went from 178.0 MB to 164.0 MB.
Total: 20.503700 ms (FindLiveObjects: 0.948900 ms CreateObjectMapping: 1.316800 ms MarkObjects: 8.799500 ms  DeleteObjects: 9.436200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.824 seconds
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000011f36f4582e (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000011f36f45653 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000011f36f4532b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000011f36f4484e (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000011f36f444ea (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000011f36f40c9b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x0000011f36f4051b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x0000011f36f40725 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x0000011f2f5b5ac2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x0000011f2f5b599b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x0000011f2f5abac3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000011f21a353fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000011f36f4582e (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000011f36f45653 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000011f36f4532b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000011f36f4484e (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000011f36f444ea (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000011f2f5abcdb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000011f21a353fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.811 seconds
Domain Reload Profiling: 1635ms
	BeginReloadAssembly (196ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (565ms)
		LoadAssemblies (408ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (232ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (209ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (812ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (656ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (192ms)
			ProcessInitializeOnLoadAttributes (341ms)
			ProcessInitializeOnLoadMethodAttributes (96ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 3.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8284 unused Assets / (13.3 MB). Loaded Objects now: 10901.
Memory consumption went from 178.1 MB to 164.8 MB.
Total: 16.106400 ms (FindLiveObjects: 1.017300 ms CreateObjectMapping: 1.505300 ms MarkObjects: 6.412600 ms  DeleteObjects: 7.169500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 378.290879 seconds.
  path: Assets/UI Toolkit/Generated/GeneratedUI_20250527_201052.uxml
  artifactKey: Guid(eae048b9e6953044c8c6c3bf06a69990) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/UI Toolkit/Generated/GeneratedUI_20250527_201052.uxml using Guid(eae048b9e6953044c8c6c3bf06a69990) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c71a2ad2ea6c90334e110b917a2b66d2') in 0.2107692 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.818 seconds
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000011f36f44ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000011f36f44cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000011f36f449cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000011f36f43eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000011f36f43b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000011f36f4033b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x0000011f36f3f7db (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x0000011f36f3f9e5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x0000011f2f505ac2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x0000011f2f50599b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x0000011f2f4fbac3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000011f408e53fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000011f36f44ece (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000011f36f44cf3 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000011f36f449cb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000011f36f43eee (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000011f36f43b8a (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000011f2f4fbcdb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000011f408e53fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.853 seconds
Domain Reload Profiling: 1671ms
	BeginReloadAssembly (201ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (60ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (556ms)
		LoadAssemblies (396ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (233ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (214ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (854ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (690ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (198ms)
			ProcessInitializeOnLoadAttributes (336ms)
			ProcessInitializeOnLoadMethodAttributes (128ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 3.38 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8284 unused Assets / (13.8 MB). Loaded Objects now: 10903.
Memory consumption went from 178.1 MB to 164.3 MB.
Total: 15.762500 ms (FindLiveObjects: 0.921700 ms CreateObjectMapping: 1.128200 ms MarkObjects: 6.519700 ms  DeleteObjects: 7.191300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.819 seconds
Refreshing native plugins compatible for Editor in 2.05 ms, found 4 plugins.
Native extension for Android target not found
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000011f35d4582e (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000011f35d45653 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000011f35d4532b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000011f35d4484e (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000011f35d444ea (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000011f35d40c9b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
0x0000011f35d4051b (Mono JIT Code) UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
0x0000011f35d40725 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa373933f5 (mono-2.0-bdwgc) mono_runtime_class_init_full (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:563)
0x00007ffa372c9f67 (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall.c:1292)
0x00007ffa372fcafc (mono-2.0-bdwgc) ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw (at C:/build/output/Unity-Technologies/mono/mono/metadata/icall-def.h:756)
0x0000011f24315ac2 (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x0000011f2431599b (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x0000011f2430bac3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000011f36ea53fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <b9a700c96f8049b6b8601ee661be52d7>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x00037] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00017] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
0x00007ffa39c614fe (Unity) StackWalker::ShowCallstack
0x00007ffa39c70b39 (Unity) PlatformStacktrace::GetStacktrace
0x00007ffa3aeae1fe (Unity) Stacktrace::GetStacktrace
0x00007ffa3b483bff (Unity) DebugStringToFile
0x00007ffa38d92b30 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000011f35d4582e (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
0x0000011f35d45653 (Mono JIT Code) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000011f35d4532b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000011f35d4484e (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000011f35d444ea (Mono JIT Code) UnityEngine.Debug:LogError (object)
0x0000011f2430bcdb (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000011f36ea53fb (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffa374569ee (mono-2.0-bdwgc) mono_jit_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/mini/mini-runtime.c:3445)
0x00007ffa37398514 (mono-2.0-bdwgc) do_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3068)
0x00007ffa37398600 (mono-2.0-bdwgc) mono_runtime_invoke (at C:/build/output/Unity-Technologies/mono/mono/metadata/object.c:3115)
0x00007ffa39b8cb34 (Unity) scripting_method_invoke
0x00007ffa39b650c3 (Unity) ScriptingInvocation::Invoke
0x00007ffa39b607c5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ffa39cd35e3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ffa39b5bd6f (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ffa39b531d3 (Unity) MonoManager::FinalizeReload
0x00007ffa3b0523a5 (Unity) ScriptingInitializer::FinalizeReload
0x00007ffa3b0a0bd7 (Unity) AssetImportWorkerClient::ReloadScriptingDomain
0x00007ffa3b07936d (Unity) <lambda_69075bd591b85779a725f8a0375a1fd8>::operator()
0x00007ffa3b15c98f (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ffa3b14a151 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ffa3b14b494 (Unity) asio::detail::win_iocp_io_context::run
0x00007ffa3b15aff9 (Unity) IOService::Run
0x00007ffa3b0a4b25 (Unity) RunAssetImportWorkerClientV2
0x00007ffa3b0a4beb (Unity) RunAssetImporterV2
0x00007ffa3a8d6c40 (Unity) Application::InitializeProject
0x00007ffa3aeba4e5 (Unity) UnityMain
0x00007ff7ee0c2f2a (Unity) __scrt_common_main_seh
0x00007ffbcd07259d (KERNEL32) BaseThreadInitThunk
0x00007ffbcdeaaf58 (ntdll) RtlUserThreadStart

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.791 seconds
Domain Reload Profiling: 1610ms
	BeginReloadAssembly (202ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (557ms)
		LoadAssemblies (404ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (229ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (206ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (791ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (636ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (192ms)
			ProcessInitializeOnLoadAttributes (322ms)
			ProcessInitializeOnLoadMethodAttributes (95ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 3.43 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 84 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8284 unused Assets / (12.6 MB). Loaded Objects now: 10905.
Memory consumption went from 178.2 MB to 165.6 MB.
Total: 14.495200 ms (FindLiveObjects: 0.856100 ms CreateObjectMapping: 1.132100 ms MarkObjects: 6.986900 ms  DeleteObjects: 5.518200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 585.584035 seconds.
  path: Assets/Editor/AIStaff/GeminiUIGeneratorWindow.cs
  artifactKey: Guid(5efcc33030f48024fb153f9a0cf878ad) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Editor/AIStaff/GeminiUIGeneratorWindow.cs using Guid(5efcc33030f48024fb153f9a0cf878ad) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '944def3d96e99bf5d79ae76786e518ea') in 0.1458415 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/Editor/AIStaff/GeminiUIGeneratorWindow.uxml
  artifactKey: Guid(db3e22cb339c6e748b39ac306fe68ddd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Editor/AIStaff/GeminiUIGeneratorWindow.uxml using Guid(db3e22cb339c6e748b39ac306fe68ddd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9f5bab4bcfb769917f22d7a177d25ef7') in 0.0684957 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0