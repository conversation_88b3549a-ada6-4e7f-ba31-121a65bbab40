.inventory-panel {
  background-color: rgba(30, 30, 30, 1);
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 10px;
}

.search-bar {
  background-color: rgba(40, 40, 40, 1);
  border-radius: 5px;
  margin-bottom: 10px;
  padding: 5px;
  color: white;
}

.category-tabs {
  flex-direction: row;
  margin-bottom: 10px;
}

.category-tab {
  background-color: rgba(40, 40, 40, 1);
  border-radius: 5px;
  margin-right: 5px;
  padding: 5px 10px;
  color: white;
  :hover {
    background-color: rgba(50, 50, 50, 1);
  }
  :active {
    background-color: rgba(60, 60, 60, 1);
  }
}

.inventory-grid {
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
}

.inventory-slot {
  width: calc(100% / 6);
  height: 50px;
  border: 1px solid rgba(50, 50, 50, 1);
  margin: 2px;
  background-color: rgba(40, 40, 40, 1);
  :hover {
    background-color: rgba(50, 50, 50, 1);
  }
  :active {
    background-color: rgba(60, 60, 60, 1);
  }
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.item-icon {
  width: 32px;
  height: 32px;
}

.item-quantity {
  color: white;
  font-size: 12px;
}