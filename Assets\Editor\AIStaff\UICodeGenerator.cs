using System;
using System.IO;
using System.Text.RegularExpressions;
using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;
using System.Collections.Generic;

namespace AIStaff.UIGenerator
{
    public class UICodeGenerator
    {
        private const string GENERATED_UI_FOLDER = "Assets/UI Toolkit/Generated";
        private const string UXML_EXTENSION = ".uxml";
        private const string USS_EXTENSION = ".uss";

        public void ApplyGeneratedCode(string uxmlCode, string ussCode)
        {
            try
            {
                // Clean and validate code before applying
                string cleanedUXML = CleanUXMLCode(uxmlCode);
                string cleanedUSS = "";

                ValidateUXMLCode(cleanedUXML);

                if (!string.IsNullOrEmpty(ussCode))
                {
                    cleanedUSS = CleanUSSCode(ussCode);
                    ValidateUSSCode(cleanedUSS);
                }

                // Create generated UI folder if it doesn't exist
                EnsureGeneratedUIFolderExists();

                // Generate unique file names
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string uxmlFileName = $"GeneratedUI_{timestamp}{UXML_EXTENSION}";
                string ussFileName = $"GeneratedUI_{timestamp}{USS_EXTENSION}";

                string uxmlPath = Path.Combine(GENERATED_UI_FOLDER, uxmlFileName);
                string ussPath = Path.Combine(GENERATED_UI_FOLDER, ussFileName);

                // Save UXML file with cleaned code
                SaveUXMLFile(uxmlPath, cleanedUXML, !string.IsNullOrEmpty(cleanedUSS) ? ussPath : "");

                // Save USS file if provided
                if (!string.IsNullOrEmpty(cleanedUSS))
                {
                    SaveUSSFile(ussPath, cleanedUSS);
                }

                // Refresh asset database
                AssetDatabase.Refresh();

                // Select the created files in the project window
                var uxmlAsset = AssetDatabase.LoadAssetAtPath<VisualTreeAsset>(uxmlPath);
                if (uxmlAsset != null)
                {
                    EditorGUIUtility.PingObject(uxmlAsset);
                    Selection.activeObject = uxmlAsset;
                }

                Debug.Log($"Generated UI files saved (cleaned and validated):\nUXML: {uxmlPath}\nUSS: {ussPath}");

                // Log what was cleaned
                if (uxmlCode != cleanedUXML)
                {
                    Debug.Log("UXML code was cleaned and fixed for Unity compatibility");
                }
                if (ussCode != cleanedUSS)
                {
                    Debug.Log("USS code was cleaned - removed unsupported CSS features");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to apply generated code: {ex.Message}", ex);
            }
        }

        public void ApplyToSelectedGameObject(string uxmlCode, string ussCode)
        {
            try
            {
                GameObject selectedObject = Selection.activeGameObject;
                if (selectedObject == null)
                {
                    throw new Exception("No GameObject selected. Please select a GameObject to apply the UI to.");
                }

                // Check if the selected object has a UIDocument component
                UIDocument uiDocument = selectedObject.GetComponent<UIDocument>();
                if (uiDocument == null)
                {
                    // Add UIDocument component if it doesn't exist
                    uiDocument = selectedObject.AddComponent<UIDocument>();
                }

                // Apply the generated code
                ApplyGeneratedCode(uxmlCode, ussCode);

                // Get the latest generated UXML file
                string latestUXMLPath = GetLatestGeneratedUXMLPath();
                if (!string.IsNullOrEmpty(latestUXMLPath))
                {
                    var visualTreeAsset = AssetDatabase.LoadAssetAtPath<VisualTreeAsset>(latestUXMLPath);
                    if (visualTreeAsset != null)
                    {
                        uiDocument.visualTreeAsset = visualTreeAsset;
                        EditorUtility.SetDirty(uiDocument);
                    }
                }

                Debug.Log($"Applied generated UI to GameObject: {selectedObject.name}");
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to apply UI to selected GameObject: {ex.Message}", ex);
            }
        }

        public void CreateNewUIDocument(string uxmlCode, string ussCode, string documentName = "")
        {
            try
            {
                // Create a new GameObject with UIDocument
                string objectName = string.IsNullOrEmpty(documentName) ? "Generated UI" : documentName;
                GameObject uiObject = new GameObject(objectName);

                // Add UIDocument component
                UIDocument uiDocument = uiObject.AddComponent<UIDocument>();

                // Apply the generated code
                ApplyGeneratedCode(uxmlCode, ussCode);

                // Get the latest generated UXML file
                string latestUXMLPath = GetLatestGeneratedUXMLPath();
                if (!string.IsNullOrEmpty(latestUXMLPath))
                {
                    var visualTreeAsset = AssetDatabase.LoadAssetAtPath<VisualTreeAsset>(latestUXMLPath);
                    if (visualTreeAsset != null)
                    {
                        uiDocument.visualTreeAsset = visualTreeAsset;
                    }
                }

                // Select the new object
                Selection.activeGameObject = uiObject;

                // Register undo
                Undo.RegisterCreatedObjectUndo(uiObject, "Create Generated UI");

                Debug.Log($"Created new UI GameObject: {objectName}");
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to create new UI document: {ex.Message}", ex);
            }
        }

        private void ValidateUXMLCode(string uxmlCode)
        {
            if (string.IsNullOrEmpty(uxmlCode))
            {
                throw new Exception("UXML code is empty");
            }

            // Clean and fix common AI mistakes
            uxmlCode = CleanUXMLCode(uxmlCode);

            // Basic UXML validation
            if (!uxmlCode.TrimStart().StartsWith("<"))
            {
                throw new Exception("UXML code must start with an XML tag");
            }

            // Check for required UXML namespace
            if (!uxmlCode.Contains("xmlns:ui=\"UnityEngine.UIElements\""))
            {
                // Add the namespace if missing
                if (uxmlCode.Contains("<ui:UXML"))
                {
                    uxmlCode = uxmlCode.Replace("<ui:UXML", "<ui:UXML xmlns:ui=\"UnityEngine.UIElements\"");
                }
                else if (uxmlCode.Contains("<UXML"))
                {
                    uxmlCode = uxmlCode.Replace("<UXML", "<ui:UXML xmlns:ui=\"UnityEngine.UIElements\"");
                }
            }

            // Validate single root element
            ValidateSingleRootElement(uxmlCode);

            // Basic XML structure validation
            try
            {
                System.Xml.XmlDocument xmlDoc = new System.Xml.XmlDocument();
                xmlDoc.LoadXml(uxmlCode);

                // Additional Unity-specific validation
                ValidateUnityElements(xmlDoc);
            }
            catch (System.Xml.XmlException ex)
            {
                throw new Exception($"Invalid UXML structure: {ex.Message}");
            }
        }

        private string CleanUXMLCode(string uxmlCode)
        {
            // Remove common AI mistakes
            var lines = uxmlCode.Split('\n');
            var cleanedLines = new List<string>();
            bool insideUxml = false;

            // Element replacement map for common AI mistakes
            var elementReplacements = new Dictionary<string, string>
            {
                { "Panel", "VisualElement" },
                { "Container", "VisualElement" },
                { "Div", "VisualElement" },
                { "Span", "Label" },
                { "Section", "VisualElement" },
                { "Header", "VisualElement" },
                { "Footer", "VisualElement" },
                { "Nav", "VisualElement" },
                { "Main", "VisualElement" },
                { "Form", "VisualElement" },
                { "Input", "TextField" },
                { "Select", "DropdownField" },
                { "Canvas", "VisualElement" }
            };

            foreach (var line in lines)
            {
                string trimmedLine = line.Trim();

                // Skip empty lines at the beginning
                if (!insideUxml && string.IsNullOrEmpty(trimmedLine))
                    continue;

                // Start of UXML
                if (trimmedLine.StartsWith("<ui:UXML") || trimmedLine.StartsWith("<UXML"))
                {
                    insideUxml = true;
                    cleanedLines.Add(line);
                    continue;
                }

                // End of UXML
                if (trimmedLine.StartsWith("</ui:UXML") || trimmedLine.StartsWith("</UXML"))
                {
                    cleanedLines.Add(line);
                    break; // Stop processing after UXML ends
                }

                // Only process lines inside UXML
                if (insideUxml)
                {
                    string cleanedLine = line;

                    // Replace invalid elements with valid ones
                    foreach (var replacement in elementReplacements)
                    {
                        string invalidElement = replacement.Key;
                        string validElement = replacement.Value;

                        // Replace opening tags
                        cleanedLine = cleanedLine.Replace($"<ui:{invalidElement}", $"<ui:{validElement}");
                        cleanedLine = cleanedLine.Replace($"<{invalidElement}", $"<ui:{validElement}");

                        // Replace closing tags
                        cleanedLine = cleanedLine.Replace($"</ui:{invalidElement}>", $"</ui:{validElement}>");
                        cleanedLine = cleanedLine.Replace($"</{invalidElement}>", $"</ui:{validElement}>");

                        // Replace self-closing tags
                        cleanedLine = cleanedLine.Replace($"<ui:{invalidElement}", $"<ui:{validElement}");
                        cleanedLine = cleanedLine.Replace($"<{invalidElement}", $"<ui:{validElement}");
                    }

                    // Clean problematic attributes
                    cleanedLine = CleanUXMLAttributes(cleanedLine);

                    cleanedLines.Add(cleanedLine);
                }
            }

            return string.Join("\n", cleanedLines);
        }

        private string CleanUXMLAttributes(string line)
        {
            // Remove CSS color functions from attributes
            line = System.Text.RegularExpressions.Regex.Replace(line, @"rgb\([^)]+\)", "white");
            line = System.Text.RegularExpressions.Regex.Replace(line, @"rgba\([^)]+\)", "white");
            line = System.Text.RegularExpressions.Regex.Replace(line, @"hsl\([^)]+\)", "white");
            line = System.Text.RegularExpressions.Regex.Replace(line, @"var\([^)]+\)", "white");

            // Remove problematic style attributes
            line = System.Text.RegularExpressions.Regex.Replace(line, @"style=""[^""]*:hover[^""]*""", "");
            line = System.Text.RegularExpressions.Regex.Replace(line, @"style=""[^""]*:active[^""]*""", "");
            line = System.Text.RegularExpressions.Regex.Replace(line, @"style=""[^""]*:focus[^""]*""", "");

            return line;
        }

        private void ValidateSingleRootElement(string uxmlCode)
        {
            try
            {
                System.Xml.XmlDocument xmlDoc = new System.Xml.XmlDocument();
                xmlDoc.LoadXml(uxmlCode);

                // Find the UXML root element
                var uxmlElement = xmlDoc.DocumentElement;
                if (uxmlElement == null || uxmlElement.LocalName != "UXML")
                {
                    throw new Exception("UXML must have <ui:UXML> as root element");
                }

                // Count direct children (should be exactly 1)
                int childElementCount = 0;
                foreach (System.Xml.XmlNode child in uxmlElement.ChildNodes)
                {
                    if (child.NodeType == System.Xml.XmlNodeType.Element)
                    {
                        childElementCount++;
                    }
                }

                if (childElementCount == 0)
                {
                    throw new Exception("UXML must contain at least one UI element");
                }

                if (childElementCount > 1)
                {
                    throw new Exception("UXML must have exactly ONE root UI element. Multiple root elements found. Wrap them in a single VisualElement.");
                }
            }
            catch (System.Xml.XmlException ex)
            {
                throw new Exception($"UXML validation failed: {ex.Message}");
            }
        }

        private void ValidateUnityElements(System.Xml.XmlDocument xmlDoc)
        {
            // Unity UI Toolkit elements that are guaranteed to work
            var validElements = new HashSet<string>
            {
                // Core elements
                "VisualElement", "Label", "Button", "TextField", "ScrollView",
                "Foldout", "Toggle", "Slider", "ProgressBar", "Image",

                // Input fields
                "DropdownField", "IntegerField", "FloatField", "Vector2Field",
                "Vector3Field", "ColorField",

                // Layout elements
                "Box", "TwoPaneSplitView",

                // Special elements
                "Style", "Template"
            };

            // Elements that should NOT be used (common AI mistakes)
            var invalidElements = new HashSet<string>
            {
                "Panel", "Container", "Div", "Span", "Section", "Article", "Header", "Footer",
                "Nav", "Main", "Aside", "Form", "Input", "Select", "Option", "Textarea",
                "Canvas", "Rect", "Circle", "Path", "SVG", "G", "Defs", "Use",
                "ListView", "TreeView", "MultiColumnTreeView", // These need special setup
                "IMGUIContainer", "PropertyField", "ObjectField", // Editor-only elements
                "Toolbar", "ToolbarButton", "ToolbarToggle", "ToolbarMenu",
                "ToolbarSearchField", "ToolbarSpacer", // Toolbar elements
                "HelpBox", "Space", "FlexibleSpace", "EnumField" // Problematic elements
            };

            ValidateElementsRecursive(xmlDoc.DocumentElement, validElements, invalidElements);
        }

        private void ValidateElementsRecursive(System.Xml.XmlNode node, HashSet<string> validElements, HashSet<string> invalidElements)
        {
            if (node.NodeType == System.Xml.XmlNodeType.Element)
            {
                string elementName = node.LocalName;

                // Skip UXML root element
                if (elementName == "UXML")
                {
                    foreach (System.Xml.XmlNode child in node.ChildNodes)
                    {
                        ValidateElementsRecursive(child, validElements, invalidElements);
                    }
                    return;
                }

                // Check for explicitly invalid elements
                if (invalidElements.Contains(elementName))
                {
                    throw new Exception($"Invalid Unity UI Toolkit element: '{elementName}'. This element is not supported or causes runtime errors. Use VisualElement instead.");
                }

                // Check if element is valid Unity UI Toolkit element
                if (!validElements.Contains(elementName))
                {
                    throw new Exception($"Unknown Unity UI Toolkit element: '{elementName}'. Only use supported Unity UI Toolkit elements like VisualElement, Label, Button, TextField, etc.");
                }

                // Validate attributes for color values
                ValidateElementAttributes(node);
            }

            // Recursively validate children
            foreach (System.Xml.XmlNode child in node.ChildNodes)
            {
                ValidateElementsRecursive(child, validElements, invalidElements);
            }
        }

        private void ValidateElementAttributes(System.Xml.XmlNode node)
        {
            if (node.Attributes == null) return;

            foreach (System.Xml.XmlAttribute attr in node.Attributes)
            {
                string attrName = attr.Name.ToLower();
                string attrValue = attr.Value;

                // Check for problematic color values
                if (attrName.Contains("color") || attrName.Contains("background"))
                {
                    ValidateColorValue(attrValue, attrName);
                }

                // Check for problematic style values
                if (attrName == "style")
                {
                    ValidateStyleAttribute(attrValue);
                }
            }
        }

        private void ValidateColorValue(string colorValue, string attributeName)
        {
            if (string.IsNullOrEmpty(colorValue)) return;

            // Check for CSS variables (not supported in Unity)
            if (colorValue.Contains("var("))
            {
                throw new Exception($"CSS variables like 'var()' are not supported in Unity UI Toolkit. Use direct color values or Unity color variables like '--unity-colors-default-text'.");
            }

            // Check for CSS functions
            if (colorValue.Contains("rgb(") || colorValue.Contains("rgba(") || colorValue.Contains("hsl("))
            {
                Debug.LogWarning($"CSS color functions might not work properly in Unity. Consider using hex colors or Unity color variables.");
            }

            // Check for invalid color formats
            if (colorValue.Contains("transparent") && !colorValue.Equals("transparent"))
            {
                Debug.LogWarning($"Complex color expressions might not work in Unity. Use simple color values.");
            }
        }

        private void ValidateStyleAttribute(string styleValue)
        {
            if (string.IsNullOrEmpty(styleValue)) return;

            var properties = styleValue.Split(';');
            foreach (var property in properties)
            {
                if (string.IsNullOrEmpty(property.Trim())) continue;

                var parts = property.Split(':');
                if (parts.Length != 2) continue;

                string propName = parts[0].Trim().ToLower();
                string propValue = parts[1].Trim();

                // Check for unsupported properties in inline styles
                if (propName.Contains("hover") || propName.Contains("active") || propName.Contains("focus"))
                {
                    throw new Exception($"Pseudo-classes in inline styles are not supported: {propName}");
                }

                // Check for CSS variables in inline styles
                if (propValue.Contains("var("))
                {
                    throw new Exception($"CSS variables in inline styles are not supported in Unity: {propValue}");
                }

                // Check for problematic color values in inline styles
                if (propName.Contains("color") || propName.Contains("background"))
                {
                    ValidateColorValue(propValue, propName);
                }
            }
        }

        private void ValidateUSSCode(string ussCode)
        {
            if (string.IsNullOrEmpty(ussCode))
            {
                return; // USS is optional
            }

            // Clean USS code from unsupported features
            ussCode = CleanUSSCode(ussCode);

            // Basic USS validation - check for common syntax errors
            var lines = ussCode.Split('\n');
            int braceCount = 0;

            for (int i = 0; i < lines.Length; i++)
            {
                string line = lines[i].Trim();
                if (string.IsNullOrEmpty(line) || line.StartsWith("/*") || line.StartsWith("//"))
                    continue;

                braceCount += CountChar(line, '{');
                braceCount -= CountChar(line, '}');

                if (braceCount < 0)
                {
                    throw new Exception($"USS syntax error: Unmatched closing brace at line {i + 1}");
                }

                // Check for unsupported CSS features
                ValidateUSSLine(line, i + 1);
            }

            if (braceCount != 0)
            {
                throw new Exception("USS syntax error: Unmatched braces");
            }
        }

        private string CleanUSSCode(string ussCode)
        {
            var lines = ussCode.Split('\n');
            var cleanedLines = new List<string>();
            bool insideRule = false;
            string currentSelector = "";

            foreach (var line in lines)
            {
                string trimmedLine = line.Trim();

                // Skip empty lines and comments
                if (string.IsNullOrEmpty(trimmedLine) || trimmedLine.StartsWith("/*") || trimmedLine.StartsWith("//"))
                {
                    cleanedLines.Add(line);
                    continue;
                }

                // Check if this is a selector line
                if (trimmedLine.EndsWith("{") && !insideRule)
                {
                    currentSelector = trimmedLine.Replace("{", "").Trim();

                    // Remove unsupported pseudo-classes
                    if (currentSelector.Contains(":hover") || currentSelector.Contains(":active") ||
                        currentSelector.Contains(":focus") || currentSelector.Contains(":visited"))
                    {
                        Debug.LogWarning($"Removing unsupported CSS pseudo-class from selector: {currentSelector}");
                        continue; // Skip this entire rule
                    }

                    insideRule = true;
                    cleanedLines.Add(line);
                    continue;
                }

                // End of rule
                if (trimmedLine == "}" && insideRule)
                {
                    insideRule = false;
                    currentSelector = "";
                    cleanedLines.Add(line);
                    continue;
                }

                // Inside a rule - validate properties
                if (insideRule)
                {
                    if (IsValidUSSProperty(trimmedLine))
                    {
                        cleanedLines.Add(line);
                    }
                    else
                    {
                        Debug.LogWarning($"Removing unsupported USS property: {trimmedLine}");
                    }
                }
                else
                {
                    cleanedLines.Add(line);
                }
            }

            return string.Join("\n", cleanedLines);
        }

        private bool IsValidUSSProperty(string line)
        {
            if (string.IsNullOrEmpty(line) || !line.Contains(":"))
                return true;

            string property = line.Split(':')[0].Trim();

            // List of unsupported CSS properties in Unity
            var unsupportedProperties = new HashSet<string>
            {
                "animation", "transition", "transform", "box-shadow", "text-shadow",
                "gradient", "filter", "backdrop-filter", "clip-path", "mask",
                "outline", "resize", "cursor", "pointer-events", "user-select",
                "z-index", "float", "clear", "table-layout", "border-collapse",
                "border-spacing", "caption-side", "empty-cells", "list-style",
                "quotes", "counter-reset", "counter-increment", "content"
            };

            // Check for unsupported properties
            if (unsupportedProperties.Contains(property.ToLower()))
            {
                return false;
            }

            // Check for CSS animations/transitions
            if (property.ToLower().Contains("animation") || property.ToLower().Contains("transition"))
            {
                return false;
            }

            return true;
        }

        private void ValidateUSSLine(string line, int lineNumber)
        {
            // Check for unsupported pseudo-classes in selectors
            if (line.Contains(":hover") || line.Contains(":active") || line.Contains(":focus"))
            {
                throw new Exception($"USS error at line {lineNumber}: Pseudo-classes (:hover, :active, :focus) are not supported in Unity UI Toolkit");
            }

            // Check for CSS animations
            if (line.ToLower().Contains("animation") || line.ToLower().Contains("transition"))
            {
                throw new Exception($"USS error at line {lineNumber}: CSS animations and transitions are not supported in Unity UI Toolkit");
            }

            // Check for unsupported CSS functions
            if (line.Contains("calc(") || line.Contains("var(") || line.Contains("rgb(") || line.Contains("rgba("))
            {
                Debug.LogWarning($"USS warning at line {lineNumber}: CSS functions might not be fully supported in Unity UI Toolkit");
            }
        }

        private int CountChar(string str, char ch)
        {
            int count = 0;
            foreach (char c in str)
            {
                if (c == ch) count++;
            }
            return count;
        }

        private void EnsureGeneratedUIFolderExists()
        {
            if (!AssetDatabase.IsValidFolder(GENERATED_UI_FOLDER))
            {
                // Create the folder structure
                string[] folders = GENERATED_UI_FOLDER.Split('/');
                string currentPath = folders[0]; // "Assets"

                for (int i = 1; i < folders.Length; i++)
                {
                    string newPath = currentPath + "/" + folders[i];
                    if (!AssetDatabase.IsValidFolder(newPath))
                    {
                        AssetDatabase.CreateFolder(currentPath, folders[i]);
                    }
                    currentPath = newPath;
                }
            }
        }

        private void SaveUXMLFile(string path, string uxmlCode, string ussPath)
        {
            // Enhance UXML with USS reference if USS file will be created
            string enhancedUXML = uxmlCode;

            if (!string.IsNullOrEmpty(ussPath))
            {
                // Check if UXML already has a Style reference
                if (!uxmlCode.Contains("<Style") && !uxmlCode.Contains("<ui:Style"))
                {
                    // Add Style reference after the UXML opening tag
                    string ussReference = $"    <Style src=\"project://database/{ussPath}\" />\n";

                    // Find the position after the opening UXML tag
                    int insertPos = uxmlCode.IndexOf('>');
                    if (insertPos != -1)
                    {
                        insertPos++;
                        // Find the end of the line
                        int lineEnd = uxmlCode.IndexOf('\n', insertPos);
                        if (lineEnd != -1)
                        {
                            enhancedUXML = uxmlCode.Insert(lineEnd + 1, ussReference);
                        }
                        else
                        {
                            enhancedUXML = uxmlCode.Insert(insertPos, "\n" + ussReference);
                        }
                    }
                }
            }

            File.WriteAllText(path, enhancedUXML);
        }

        private void SaveUSSFile(string path, string ussCode)
        {
            File.WriteAllText(path, ussCode);
        }

        private string GetLatestGeneratedUXMLPath()
        {
            if (!Directory.Exists(GENERATED_UI_FOLDER))
                return null;

            var uxmlFiles = Directory.GetFiles(GENERATED_UI_FOLDER, "*" + UXML_EXTENSION);
            if (uxmlFiles.Length == 0)
                return null;

            // Sort by creation time and return the latest
            Array.Sort(uxmlFiles, (x, y) => File.GetCreationTime(y).CompareTo(File.GetCreationTime(x)));
            return uxmlFiles[0];
        }

        public List<string> GetGeneratedUIFiles()
        {
            var files = new List<string>();

            if (Directory.Exists(GENERATED_UI_FOLDER))
            {
                var uxmlFiles = Directory.GetFiles(GENERATED_UI_FOLDER, "*" + UXML_EXTENSION);
                foreach (var file in uxmlFiles)
                {
                    files.Add(Path.GetFileName(file));
                }
            }

            return files;
        }

        public void DeleteGeneratedFile(string fileName)
        {
            try
            {
                string filePath = Path.Combine(GENERATED_UI_FOLDER, fileName);
                if (File.Exists(filePath))
                {
                    AssetDatabase.DeleteAsset(filePath);

                    // Also delete corresponding USS file if it exists
                    string ussFileName = Path.ChangeExtension(fileName, USS_EXTENSION);
                    string ussPath = Path.Combine(GENERATED_UI_FOLDER, ussFileName);
                    if (File.Exists(ussPath))
                    {
                        AssetDatabase.DeleteAsset(ussPath);
                    }

                    AssetDatabase.Refresh();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to delete generated file {fileName}: {ex.Message}");
            }
        }

        public string FormatUXMLCode(string uxmlCode)
        {
            try
            {
                // Basic UXML formatting
                System.Xml.XmlDocument xmlDoc = new System.Xml.XmlDocument();
                xmlDoc.LoadXml(uxmlCode);

                using (var stringWriter = new StringWriter())
                using (var xmlWriter = System.Xml.XmlWriter.Create(stringWriter, new System.Xml.XmlWriterSettings
                {
                    Indent = true,
                    IndentChars = "    ",
                    NewLineChars = "\n"
                }))
                {
                    xmlDoc.Save(xmlWriter);
                    return stringWriter.ToString();
                }
            }
            catch
            {
                // Return original code if formatting fails
                return uxmlCode;
            }
        }

        public string FormatUSSCode(string ussCode)
        {
            if (string.IsNullOrEmpty(ussCode))
                return ussCode;

            try
            {
                // Basic USS formatting
                var lines = ussCode.Split('\n');
                var formattedLines = new List<string>();
                int indentLevel = 0;

                foreach (var line in lines)
                {
                    string trimmedLine = line.Trim();
                    if (string.IsNullOrEmpty(trimmedLine))
                    {
                        formattedLines.Add("");
                        continue;
                    }

                    if (trimmedLine.Contains('}'))
                        indentLevel--;

                    string indent = new string(' ', Math.Max(0, indentLevel * 4));
                    formattedLines.Add(indent + trimmedLine);

                    if (trimmedLine.Contains('{'))
                        indentLevel++;
                }

                return string.Join("\n", formattedLines);
            }
            catch
            {
                // Return original code if formatting fails
                return ussCode;
            }
        }
    }
}
