using System;
using System.IO;
using System.Text.RegularExpressions;
using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;
using System.Collections.Generic;

namespace AIStaff.UIGenerator
{
    public class UICodeGenerator
    {
        private const string GENERATED_UI_FOLDER = "Assets/UI Toolkit/Generated";
        private const string UXML_EXTENSION = ".uxml";
        private const string USS_EXTENSION = ".uss";

        public void ApplyGeneratedCode(string uxmlCode, string ussCode)
        {
            try
            {
                // Validate code before applying
                ValidateUXMLCode(uxmlCode);
                if (!string.IsNullOrEmpty(ussCode))
                {
                    ValidateUSSCode(ussCode);
                }

                // Create generated UI folder if it doesn't exist
                EnsureGeneratedUIFolderExists();

                // Generate unique file names
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string uxmlFileName = $"GeneratedUI_{timestamp}{UXML_EXTENSION}";
                string ussFileName = $"GeneratedUI_{timestamp}{USS_EXTENSION}";

                string uxmlPath = Path.Combine(GENERATED_UI_FOLDER, uxmlFileName);
                string ussPath = Path.Combine(GENERATED_UI_FOLDER, ussFileName);

                // Save UXML file
                SaveUXMLFile(uxmlPath, uxmlCode, ussPath);

                // Save USS file if provided
                if (!string.IsNullOrEmpty(ussCode))
                {
                    SaveUSSFile(ussPath, ussCode);
                }

                // Refresh asset database
                AssetDatabase.Refresh();

                // Select the created files in the project window
                var uxmlAsset = AssetDatabase.LoadAssetAtPath<VisualTreeAsset>(uxmlPath);
                if (uxmlAsset != null)
                {
                    EditorGUIUtility.PingObject(uxmlAsset);
                    Selection.activeObject = uxmlAsset;
                }

                Debug.Log($"Generated UI files saved:\nUXML: {uxmlPath}\nUSS: {ussPath}");
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to apply generated code: {ex.Message}", ex);
            }
        }

        public void ApplyToSelectedGameObject(string uxmlCode, string ussCode)
        {
            try
            {
                GameObject selectedObject = Selection.activeGameObject;
                if (selectedObject == null)
                {
                    throw new Exception("No GameObject selected. Please select a GameObject to apply the UI to.");
                }

                // Check if the selected object has a UIDocument component
                UIDocument uiDocument = selectedObject.GetComponent<UIDocument>();
                if (uiDocument == null)
                {
                    // Add UIDocument component if it doesn't exist
                    uiDocument = selectedObject.AddComponent<UIDocument>();
                }

                // Apply the generated code
                ApplyGeneratedCode(uxmlCode, ussCode);

                // Get the latest generated UXML file
                string latestUXMLPath = GetLatestGeneratedUXMLPath();
                if (!string.IsNullOrEmpty(latestUXMLPath))
                {
                    var visualTreeAsset = AssetDatabase.LoadAssetAtPath<VisualTreeAsset>(latestUXMLPath);
                    if (visualTreeAsset != null)
                    {
                        uiDocument.visualTreeAsset = visualTreeAsset;
                        EditorUtility.SetDirty(uiDocument);
                    }
                }

                Debug.Log($"Applied generated UI to GameObject: {selectedObject.name}");
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to apply UI to selected GameObject: {ex.Message}", ex);
            }
        }

        public void CreateNewUIDocument(string uxmlCode, string ussCode, string documentName = "")
        {
            try
            {
                // Create a new GameObject with UIDocument
                string objectName = string.IsNullOrEmpty(documentName) ? "Generated UI" : documentName;
                GameObject uiObject = new GameObject(objectName);
                
                // Add UIDocument component
                UIDocument uiDocument = uiObject.AddComponent<UIDocument>();

                // Apply the generated code
                ApplyGeneratedCode(uxmlCode, ussCode);

                // Get the latest generated UXML file
                string latestUXMLPath = GetLatestGeneratedUXMLPath();
                if (!string.IsNullOrEmpty(latestUXMLPath))
                {
                    var visualTreeAsset = AssetDatabase.LoadAssetAtPath<VisualTreeAsset>(latestUXMLPath);
                    if (visualTreeAsset != null)
                    {
                        uiDocument.visualTreeAsset = visualTreeAsset;
                    }
                }

                // Select the new object
                Selection.activeGameObject = uiObject;
                
                // Register undo
                Undo.RegisterCreatedObjectUndo(uiObject, "Create Generated UI");

                Debug.Log($"Created new UI GameObject: {objectName}");
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to create new UI document: {ex.Message}", ex);
            }
        }

        private void ValidateUXMLCode(string uxmlCode)
        {
            if (string.IsNullOrEmpty(uxmlCode))
            {
                throw new Exception("UXML code is empty");
            }

            // Basic UXML validation
            if (!uxmlCode.TrimStart().StartsWith("<"))
            {
                throw new Exception("UXML code must start with an XML tag");
            }

            // Check for required UXML namespace
            if (!uxmlCode.Contains("xmlns:ui=\"UnityEngine.UIElements\""))
            {
                // Add the namespace if missing
                if (uxmlCode.Contains("<ui:UXML"))
                {
                    uxmlCode = uxmlCode.Replace("<ui:UXML", "<ui:UXML xmlns:ui=\"UnityEngine.UIElements\"");
                }
                else if (uxmlCode.Contains("<UXML"))
                {
                    uxmlCode = uxmlCode.Replace("<UXML", "<ui:UXML xmlns:ui=\"UnityEngine.UIElements\"");
                }
            }

            // Basic XML structure validation
            try
            {
                System.Xml.XmlDocument xmlDoc = new System.Xml.XmlDocument();
                xmlDoc.LoadXml(uxmlCode);
            }
            catch (System.Xml.XmlException ex)
            {
                throw new Exception($"Invalid UXML structure: {ex.Message}");
            }
        }

        private void ValidateUSSCode(string ussCode)
        {
            if (string.IsNullOrEmpty(ussCode))
            {
                return; // USS is optional
            }

            // Basic USS validation - check for common syntax errors
            var lines = ussCode.Split('\n');
            int braceCount = 0;
            
            for (int i = 0; i < lines.Length; i++)
            {
                string line = lines[i].Trim();
                if (string.IsNullOrEmpty(line) || line.StartsWith("/*") || line.StartsWith("//"))
                    continue;

                braceCount += CountChar(line, '{');
                braceCount -= CountChar(line, '}');

                if (braceCount < 0)
                {
                    throw new Exception($"USS syntax error: Unmatched closing brace at line {i + 1}");
                }
            }

            if (braceCount != 0)
            {
                throw new Exception("USS syntax error: Unmatched braces");
            }
        }

        private int CountChar(string str, char ch)
        {
            int count = 0;
            foreach (char c in str)
            {
                if (c == ch) count++;
            }
            return count;
        }

        private void EnsureGeneratedUIFolderExists()
        {
            if (!AssetDatabase.IsValidFolder(GENERATED_UI_FOLDER))
            {
                // Create the folder structure
                string[] folders = GENERATED_UI_FOLDER.Split('/');
                string currentPath = folders[0]; // "Assets"

                for (int i = 1; i < folders.Length; i++)
                {
                    string newPath = currentPath + "/" + folders[i];
                    if (!AssetDatabase.IsValidFolder(newPath))
                    {
                        AssetDatabase.CreateFolder(currentPath, folders[i]);
                    }
                    currentPath = newPath;
                }
            }
        }

        private void SaveUXMLFile(string path, string uxmlCode, string ussPath)
        {
            // Enhance UXML with USS reference if USS file will be created
            string enhancedUXML = uxmlCode;
            
            if (!string.IsNullOrEmpty(ussPath))
            {
                // Check if UXML already has a Style reference
                if (!uxmlCode.Contains("<Style") && !uxmlCode.Contains("<ui:Style"))
                {
                    // Add Style reference after the UXML opening tag
                    string ussReference = $"    <Style src=\"project://database/{ussPath}\" />\n";
                    
                    // Find the position after the opening UXML tag
                    int insertPos = uxmlCode.IndexOf('>');
                    if (insertPos != -1)
                    {
                        insertPos++;
                        // Find the end of the line
                        int lineEnd = uxmlCode.IndexOf('\n', insertPos);
                        if (lineEnd != -1)
                        {
                            enhancedUXML = uxmlCode.Insert(lineEnd + 1, ussReference);
                        }
                        else
                        {
                            enhancedUXML = uxmlCode.Insert(insertPos, "\n" + ussReference);
                        }
                    }
                }
            }

            File.WriteAllText(path, enhancedUXML);
        }

        private void SaveUSSFile(string path, string ussCode)
        {
            File.WriteAllText(path, ussCode);
        }

        private string GetLatestGeneratedUXMLPath()
        {
            if (!Directory.Exists(GENERATED_UI_FOLDER))
                return null;

            var uxmlFiles = Directory.GetFiles(GENERATED_UI_FOLDER, "*" + UXML_EXTENSION);
            if (uxmlFiles.Length == 0)
                return null;

            // Sort by creation time and return the latest
            Array.Sort(uxmlFiles, (x, y) => File.GetCreationTime(y).CompareTo(File.GetCreationTime(x)));
            return uxmlFiles[0];
        }

        public List<string> GetGeneratedUIFiles()
        {
            var files = new List<string>();
            
            if (Directory.Exists(GENERATED_UI_FOLDER))
            {
                var uxmlFiles = Directory.GetFiles(GENERATED_UI_FOLDER, "*" + UXML_EXTENSION);
                foreach (var file in uxmlFiles)
                {
                    files.Add(Path.GetFileName(file));
                }
            }
            
            return files;
        }

        public void DeleteGeneratedFile(string fileName)
        {
            try
            {
                string filePath = Path.Combine(GENERATED_UI_FOLDER, fileName);
                if (File.Exists(filePath))
                {
                    AssetDatabase.DeleteAsset(filePath);
                    
                    // Also delete corresponding USS file if it exists
                    string ussFileName = Path.ChangeExtension(fileName, USS_EXTENSION);
                    string ussPath = Path.Combine(GENERATED_UI_FOLDER, ussFileName);
                    if (File.Exists(ussPath))
                    {
                        AssetDatabase.DeleteAsset(ussPath);
                    }
                    
                    AssetDatabase.Refresh();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to delete generated file {fileName}: {ex.Message}");
            }
        }

        public string FormatUXMLCode(string uxmlCode)
        {
            try
            {
                // Basic UXML formatting
                System.Xml.XmlDocument xmlDoc = new System.Xml.XmlDocument();
                xmlDoc.LoadXml(uxmlCode);
                
                using (var stringWriter = new StringWriter())
                using (var xmlWriter = System.Xml.XmlWriter.Create(stringWriter, new System.Xml.XmlWriterSettings
                {
                    Indent = true,
                    IndentChars = "    ",
                    NewLineChars = "\n"
                }))
                {
                    xmlDoc.Save(xmlWriter);
                    return stringWriter.ToString();
                }
            }
            catch
            {
                // Return original code if formatting fails
                return uxmlCode;
            }
        }

        public string FormatUSSCode(string ussCode)
        {
            if (string.IsNullOrEmpty(ussCode))
                return ussCode;

            try
            {
                // Basic USS formatting
                var lines = ussCode.Split('\n');
                var formattedLines = new List<string>();
                int indentLevel = 0;
                
                foreach (var line in lines)
                {
                    string trimmedLine = line.Trim();
                    if (string.IsNullOrEmpty(trimmedLine))
                    {
                        formattedLines.Add("");
                        continue;
                    }
                    
                    if (trimmedLine.Contains('}'))
                        indentLevel--;
                    
                    string indent = new string(' ', Math.Max(0, indentLevel * 4));
                    formattedLines.Add(indent + trimmedLine);
                    
                    if (trimmedLine.Contains('{'))
                        indentLevel++;
                }
                
                return string.Join("\n", formattedLines);
            }
            catch
            {
                // Return original code if formatting fails
                return ussCode;
            }
        }
    }
}
