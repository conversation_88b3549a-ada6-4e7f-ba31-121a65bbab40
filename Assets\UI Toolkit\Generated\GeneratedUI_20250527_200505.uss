/* Base Styles */
.dashboard-layout {
    flex-grow: 1;
    background-color: #2a2a2a; /* Dark background */
}

/* Sidebar */
.sidebar {
    background-color: #333333;
    width: 200px; /* Fixed width for the sidebar */
    flex-direction: column;
    padding: 20px;
    border-right-width: 1px;
    border-color: #444444;
}

.sidebar-title {
    color: white;
    font-size: 24px;
    -unity-font-style: Bold;
    margin-bottom: 30px;
    -unity-text-align: middle-center;
}

.sidebar-navigation {
    flex-direction: column;
    flex-grow: 1;
}

.sidebar-button {
    background-color: #444444;
    color: white;
    font-size: 16px;
    padding: 10px 15px;
    margin-bottom: 10px;
    border-radius: 5px;
    border-width: 0;
    -unity-text-align: middle-left;
    flex-grow: 0;
    width: auto;
    height: auto;
}

/* Dashboard Content */
.dashboard-content-scrollview {
    flex-grow: 1;
}

.dashboard-content {
    flex-direction: column;
    padding: 30px;
    flex-grow: 1;
}

.dashboard-title {
    color: white;
    font-size: 32px;
    -unity-font-style: Bold;
    margin-bottom: 30px;
}

.section-title {
    color: white;
    font-size: 24px;
    -unity-font-style: Bold;
    margin-top: 40px;
    margin-bottom: 20px;
}

/* Cards Container */
.card-container {
    flex-direction: row; /* Arrange cards horizontally */
    flex-wrap: wrap; /* Allow cards to wrap to next line */
    justify-content: space-between; /* Distribute space between cards */
    margin-bottom: 30px;
}

.statistic-card {
    background-color: #333333;
    border-width: 1px;
    border-color: #444444;
    border-radius: 8px;
    padding: 20px;
    margin: 10px; /* Spacing around cards */
    width: 280px; /* Fixed width for cards */
    min-height: 200px;
    flex-direction: column;
    flex-grow: 0;
}

.card-header {
    color: #cccccc;
    font-size: 18px;
    -unity-font-style: Bold;
    margin-bottom: 10px;
}

.card-value {
    color: white;
    font-size: 36px;
    -unity-font-style: Bold;
    margin-bottom: 15px;
}

.chart-placeholder {
    background-color: #4a4a4a;
    height: 80px; /* Placeholder height */
    border-radius: 4px;
    margin-top: 10px;
}

/* Metrics Section */
.metrics-section {
    flex-direction: column;
    width: 600px; /* Max width for metrics section */
}

.metric-item {
    flex-direction: row; /* Label and progress bar side-by-side */
    align-items: center; /* Vertically align items */
    margin-bottom: 15px;
    padding: 10px;
    background-color: #383838;
    border-radius: 5px;
}

.metric-label {
    color: white;
    font-size: 16px;
    flex-grow: 1; /* Label takes available space */
    margin-right: 20px; /* Space between label and progress bar */
}

.metric-progress-bar {
    width: 250px; /* Fixed width for progress bar */
    height: 20px;
    background-color: #4a4a4a; /* Background of the bar track */
    border-radius: 10px;
}

/* Progress bar fill color */
.metric-progress-bar #unity-progress-bar__progress {
    background-color: #007bff; /* Blue fill color */
    border-radius: 10px;
}