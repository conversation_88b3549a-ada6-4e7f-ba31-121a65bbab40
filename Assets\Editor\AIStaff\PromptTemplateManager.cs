using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEditor;

namespace AIStaff.UIGenerator
{
    [System.Serializable]
    public class PromptTemplate
    {
        public string name;
        public string description;
        public string prompt;
        public string category;
        public DateTime createdDate;
        public DateTime lastUsed;
        public int useCount;
        public List<string> tags;

        public PromptTemplate()
        {
            tags = new List<string>();
            createdDate = DateTime.Now;
            lastUsed = DateTime.Now;
            useCount = 0;
        }
    }

    [System.Serializable]
    public class PromptTemplateCollection
    {
        public List<PromptTemplate> templates;
        public int version;

        public PromptTemplateCollection()
        {
            templates = new List<PromptTemplate>();
            version = 1;
        }
    }

    public class PromptTemplateManager
    {
        private const string TEMPLATES_FOLDER = "Assets/Editor/AIStaff/Templates";
        private const string TEMPLATES_FILE = "PromptTemplates.json";
        private const string BUILTIN_TEMPLATES_FILE = "BuiltinTemplates.json";

        private PromptTemplateCollection templateCollection;
        private string templatesFilePath;

        public PromptTemplateManager()
        {
            templatesFilePath = Path.Combine(TEMPLATES_FOLDER, TEMPLATES_FILE);
            LoadTemplates();
            EnsureBuiltinTemplates();
        }

        public void SaveTemplate(string filePath, string prompt)
        {
            try
            {
                string fileName = Path.GetFileNameWithoutExtension(filePath);
                var template = new PromptTemplate
                {
                    name = fileName,
                    prompt = prompt,
                    description = $"Template created on {DateTime.Now:yyyy-MM-dd HH:mm}",
                    category = "User Created"
                };

                // Save individual template file
                string templateJson = JsonUtility.ToJson(template, true);
                File.WriteAllText(filePath, templateJson);

                // Add to collection
                AddTemplateToCollection(template);
                SaveTemplateCollection();

                Debug.Log($"Template saved: {fileName}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to save template: {ex.Message}");
                throw;
            }
        }

        public string LoadTemplate(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"Template file not found: {filePath}");
                }

                string templateJson = File.ReadAllText(filePath);
                var template = JsonUtility.FromJson<PromptTemplate>(templateJson);

                if (template != null)
                {
                    // Update usage statistics
                    template.lastUsed = DateTime.Now;
                    template.useCount++;
                    UpdateTemplateInCollection(template);
                    SaveTemplateCollection();

                    return template.prompt;
                }

                return "";
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to load template: {ex.Message}");
                throw;
            }
        }

        public string LoadTemplateByName(string templateName)
        {
            try
            {
                var template = GetTemplateByName(templateName);
                if (template != null)
                {
                    // Update usage statistics
                    template.lastUsed = DateTime.Now;
                    template.useCount++;
                    SaveTemplateCollection();

                    return template.prompt;
                }

                return "";
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to load template by name: {ex.Message}");
                return "";
            }
        }

        public List<string> GetAvailableTemplates()
        {
            var templateNames = new List<string>();

            foreach (var template in templateCollection.templates)
            {
                templateNames.Add(template.name);
            }

            return templateNames;
        }

        public List<PromptTemplate> GetTemplatesByCategory(string category)
        {
            var templates = new List<PromptTemplate>();

            foreach (var template in templateCollection.templates)
            {
                if (string.Equals(template.category, category, StringComparison.OrdinalIgnoreCase))
                {
                    templates.Add(template);
                }
            }

            return templates;
        }

        public List<string> GetCategories()
        {
            var categories = new HashSet<string>();

            foreach (var template in templateCollection.templates)
            {
                if (!string.IsNullOrEmpty(template.category))
                {
                    categories.Add(template.category);
                }
            }

            var categoryList = new List<string>(categories);
            categoryList.Sort();
            return categoryList;
        }

        public string GetTemplatesDirectory()
        {
            EnsureTemplatesDirectoryExists();
            return TEMPLATES_FOLDER;
        }

        public void DeleteTemplate(string templateName)
        {
            try
            {
                // Remove from collection
                templateCollection.templates.RemoveAll(t => t.name == templateName);
                SaveTemplateCollection();

                // Delete individual file if it exists
                string filePath = Path.Combine(TEMPLATES_FOLDER, templateName + ".json");
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }

                Debug.Log($"Template deleted: {templateName}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to delete template: {ex.Message}");
                throw;
            }
        }

        public PromptTemplate GetTemplateByName(string name)
        {
            foreach (var template in templateCollection.templates)
            {
                if (template.name == name)
                {
                    return template;
                }
            }
            return null;
        }

        public void ExportTemplates(string exportPath)
        {
            try
            {
                string json = JsonUtility.ToJson(templateCollection, true);
                File.WriteAllText(exportPath, json);
                Debug.Log($"Templates exported to: {exportPath}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to export templates: {ex.Message}");
                throw;
            }
        }

        public void ImportTemplates(string importPath)
        {
            try
            {
                if (!File.Exists(importPath))
                {
                    throw new FileNotFoundException($"Import file not found: {importPath}");
                }

                string json = File.ReadAllText(importPath);
                var importedCollection = JsonUtility.FromJson<PromptTemplateCollection>(json);

                if (importedCollection?.templates != null)
                {
                    foreach (var template in importedCollection.templates)
                    {
                        AddTemplateToCollection(template);
                    }

                    SaveTemplateCollection();
                    Debug.Log($"Imported {importedCollection.templates.Count} templates");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to import templates: {ex.Message}");
                throw;
            }
        }

        private void LoadTemplates()
        {
            try
            {
                EnsureTemplatesDirectoryExists();

                if (File.Exists(templatesFilePath))
                {
                    string json = File.ReadAllText(templatesFilePath);
                    templateCollection = JsonUtility.FromJson<PromptTemplateCollection>(json);
                }

                if (templateCollection == null)
                {
                    templateCollection = new PromptTemplateCollection();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to load templates: {ex.Message}");
                templateCollection = new PromptTemplateCollection();
            }
        }

        private void SaveTemplateCollection()
        {
            try
            {
                EnsureTemplatesDirectoryExists();
                string json = JsonUtility.ToJson(templateCollection, true);
                File.WriteAllText(templatesFilePath, json);
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to save template collection: {ex.Message}");
            }
        }

        private void EnsureTemplatesDirectoryExists()
        {
            if (!Directory.Exists(TEMPLATES_FOLDER))
            {
                Directory.CreateDirectory(TEMPLATES_FOLDER);
            }
        }

        private void AddTemplateToCollection(PromptTemplate template)
        {
            // Remove existing template with same name
            templateCollection.templates.RemoveAll(t => t.name == template.name);

            // Add new template
            templateCollection.templates.Add(template);
        }

        private void UpdateTemplateInCollection(PromptTemplate updatedTemplate)
        {
            for (int i = 0; i < templateCollection.templates.Count; i++)
            {
                if (templateCollection.templates[i].name == updatedTemplate.name)
                {
                    templateCollection.templates[i] = updatedTemplate;
                    break;
                }
            }
        }

        private void EnsureBuiltinTemplates()
        {
            // Add built-in templates if they don't exist
            var builtinTemplates = GetBuiltinTemplates();

            foreach (var builtinTemplate in builtinTemplates)
            {
                if (GetTemplateByName(builtinTemplate.name) == null)
                {
                    AddTemplateToCollection(builtinTemplate);
                }
            }

            SaveTemplateCollection();
        }

        private List<PromptTemplate> GetBuiltinTemplates()
        {
            return new List<PromptTemplate>
            {
                new PromptTemplate
                {
                    name = "Login Form",
                    description = "A standard login form with username, password, and login button",
                    prompt = "Create a login form with username and password text fields, a login button, and a 'Forgot Password?' link. Use a modern card-style layout with rounded corners. Include proper spacing and Unity-compatible styling. DO NOT use :hover, :active, or :focus pseudo-classes. Use Unity UI Toolkit elements only.",
                    category = "Forms",
                    tags = new List<string> { "login", "form", "authentication" }
                },
                new PromptTemplate
                {
                    name = "Settings Panel",
                    description = "A settings panel with various configuration options",
                    prompt = "Create a settings panel with sections for Audio, Graphics, and Controls. Include sliders for volume controls, dropdown menus for quality settings, and toggle switches for various options. Use a clean, organized layout with clear section headers. Use only Unity UI Toolkit elements and properties. DO NOT use CSS pseudo-classes or animations.",
                    category = "Panels",
                    tags = new List<string> { "settings", "configuration", "options" }
                },
                new PromptTemplate
                {
                    name = "Inventory Grid",
                    description = "A grid-based inventory system for games",
                    prompt = "Create an inventory grid system with 6x8 slots using VisualElement containers. Each slot should be able to display an item icon and quantity. Include a search bar at the top and category tabs. Use a dark theme suitable for gaming interfaces. Use only Unity UI Toolkit elements and avoid CSS pseudo-classes.",
                    category = "Game UI",
                    tags = new List<string> { "inventory", "grid", "game", "items" }
                },
                new PromptTemplate
                {
                    name = "Dashboard Layout",
                    description = "A dashboard with statistics and charts",
                    prompt = "Create a dashboard layout with cards displaying various statistics. Include placeholder areas for charts, progress bars for metrics, and a sidebar with navigation options. Use a professional, clean design with good use of whitespace. Use only Unity UI Toolkit elements and Unity-compatible CSS properties.",
                    category = "Layouts",
                    tags = new List<string> { "dashboard", "statistics", "charts", "metrics" }
                },
                new PromptTemplate
                {
                    name = "Chat Interface",
                    description = "A chat interface with message bubbles",
                    prompt = "Create a chat interface with a scrollable message area using ScrollView, text input field, and send button. Messages should appear as bubbles with different styles for sent and received messages. Include timestamps and user avatars. Use only Unity UI Toolkit elements and avoid CSS pseudo-classes.",
                    category = "Communication",
                    tags = new List<string> { "chat", "messaging", "communication" }
                }
            };
        }
    }
}
