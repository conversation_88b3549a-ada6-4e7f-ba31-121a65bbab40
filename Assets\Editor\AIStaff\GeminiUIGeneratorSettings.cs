using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.IO;

namespace AIStaff.UIGenerator
{
    [System.Serializable]
    public class APIUsageStats
    {
        public int totalRequests;
        public int successfulRequests;
        public int failedRequests;
        public DateTime lastRequestTime;
        public float averageResponseTime;
        public List<DateTime> requestHistory;

        public APIUsageStats()
        {
            requestHistory = new List<DateTime>();
            lastRequestTime = DateTime.MinValue;
        }

        public void RecordRequest(bool success, float responseTime)
        {
            totalRequests++;
            if (success)
                successfulRequests++;
            else
                failedRequests++;

            lastRequestTime = DateTime.Now;
            requestHistory.Add(lastRequestTime);

            // Update average response time
            averageResponseTime = (averageResponseTime * (totalRequests - 1) + responseTime) / totalRequests;

            // Keep only last 100 requests in history
            if (requestHistory.Count > 100)
            {
                requestHistory.RemoveAt(0);
            }
        }

        public float GetSuccessRate()
        {
            if (totalRequests == 0) return 0f;
            return (float)successfulRequests / totalRequests * 100f;
        }
    }

    [System.Serializable]
    public class UIGenerationPreferences
    {
        public bool autoApplyGenerated = false;
        public bool showLivePreview = true;
        public bool enableSyntaxHighlighting = true;
        public bool saveGenerationHistory = true;
        public bool autoSavePrompts = true;
        public int maxHistoryEntries = 50;
        public string defaultOutputFolder = "Assets/UI Toolkit/Generated";
        public bool useCustomNaming = false;
        public string customNamingPattern = "GeneratedUI_{timestamp}";
    }

    [System.Serializable]
    public class EditorUIPreferences
    {
        public bool compactMode = false;
        public float windowOpacity = 1.0f;
        public bool showTooltips = true;
        public bool enableAnimations = true;
        public string themePreference = "Auto"; // Auto, Light, Dark
        public bool showAdvancedOptions = false;
        public Vector2 lastWindowSize = new Vector2(800, 600);
        public Vector2 lastWindowPosition = new Vector2(100, 100);
    }

    [CreateAssetMenu(fileName = "GeminiUIGeneratorSettings", menuName = "AI Staff/Gemini UI Generator Settings")]
    public class GeminiUIGeneratorSettings : ScriptableObject
    {
        private const string SETTINGS_PATH = "Assets/Editor/AIStaff/Settings";
        private const string SETTINGS_FILE = "GeminiUIGeneratorSettings.asset";

        [Header("API Configuration")]
        [SerializeField] private string _apiKey = "";
        [SerializeField] private string _selectedModel = "gemini-1.5-pro";
        [SerializeField] private float _requestTimeout = 60f;
        [SerializeField] private int _maxRetries = 3;

        [Header("Generation Settings")]
        [SerializeField] private string _lastPrompt = "";
        [SerializeField] private List<string> _promptHistory = new List<string>();
        [SerializeField] private UIGenerationPreferences _generationPreferences = new UIGenerationPreferences();

        [Header("Editor Preferences")]
        [SerializeField] private EditorUIPreferences _editorPreferences = new EditorUIPreferences();

        [Header("Statistics")]
        [SerializeField] private APIUsageStats _usageStats = new APIUsageStats();

        [Header("Advanced")]
        [SerializeField] private bool _enableDebugLogging = false;
        [SerializeField] private bool _enableTelemetry = true;
        [SerializeField] private string _customAPIEndpoint = "";

        // Public properties
        public string apiKey
        {
            get => _apiKey;
            set
            {
                _apiKey = value;
                EditorUtility.SetDirty(this);
            }
        }

        public string selectedModel
        {
            get => _selectedModel;
            set
            {
                _selectedModel = value;
                EditorUtility.SetDirty(this);
            }
        }

        public float requestTimeout
        {
            get => _requestTimeout;
            set
            {
                _requestTimeout = Mathf.Clamp(value, 10f, 300f);
                EditorUtility.SetDirty(this);
            }
        }

        public int maxRetries
        {
            get => _maxRetries;
            set
            {
                _maxRetries = Mathf.Clamp(value, 0, 10);
                EditorUtility.SetDirty(this);
            }
        }

        public string lastPrompt
        {
            get => _lastPrompt;
            set
            {
                if (_lastPrompt != value)
                {
                    _lastPrompt = value;
                    AddToPromptHistory(value);
                    EditorUtility.SetDirty(this);
                }
            }
        }

        public List<string> promptHistory => new List<string>(_promptHistory);

        public UIGenerationPreferences generationPreferences => _generationPreferences;
        public EditorUIPreferences editorPreferences => _editorPreferences;
        public APIUsageStats usageStats => _usageStats;

        public bool enableDebugLogging
        {
            get => _enableDebugLogging;
            set
            {
                _enableDebugLogging = value;
                EditorUtility.SetDirty(this);
            }
        }

        public bool enableTelemetry
        {
            get => _enableTelemetry;
            set
            {
                _enableTelemetry = value;
                EditorUtility.SetDirty(this);
            }
        }

        public string customAPIEndpoint
        {
            get => _customAPIEndpoint;
            set
            {
                _customAPIEndpoint = value;
                EditorUtility.SetDirty(this);
            }
        }

        public static GeminiUIGeneratorSettings GetOrCreateSettings()
        {
            string fullPath = Path.Combine(SETTINGS_PATH, SETTINGS_FILE);

            var settings = AssetDatabase.LoadAssetAtPath<GeminiUIGeneratorSettings>(fullPath);

            if (settings == null)
            {
                settings = CreateInstance<GeminiUIGeneratorSettings>();

                // Ensure directory exists
                if (!Directory.Exists(SETTINGS_PATH))
                {
                    Directory.CreateDirectory(SETTINGS_PATH);
                }

                AssetDatabase.CreateAsset(settings, fullPath);
                AssetDatabase.SaveAssets();
            }

            return settings;
        }

        public void AddToPromptHistory(string prompt)
        {
            if (string.IsNullOrEmpty(prompt) || !_generationPreferences.saveGenerationHistory)
                return;

            // Remove if already exists
            _promptHistory.Remove(prompt);

            // Add to beginning
            _promptHistory.Insert(0, prompt);

            // Limit history size
            while (_promptHistory.Count > _generationPreferences.maxHistoryEntries)
            {
                _promptHistory.RemoveAt(_promptHistory.Count - 1);
            }

            EditorUtility.SetDirty(this);
        }

        public void ClearPromptHistory()
        {
            _promptHistory.Clear();
            EditorUtility.SetDirty(this);
        }

        public void RecordAPIUsage(bool success, float responseTime)
        {
            _usageStats.RecordRequest(success, responseTime);
            EditorUtility.SetDirty(this);
        }

        public void ResetStatistics()
        {
            _usageStats = new APIUsageStats();
            EditorUtility.SetDirty(this);
        }

        public void ResetToDefaults()
        {
            _apiKey = "";
            _selectedModel = "gemini-1.5-pro";
            _requestTimeout = 60f;
            _maxRetries = 3;
            _lastPrompt = "";
            _promptHistory.Clear();
            _generationPreferences = new UIGenerationPreferences();
            _editorPreferences = new EditorUIPreferences();
            _enableDebugLogging = false;
            _enableTelemetry = true;
            _customAPIEndpoint = "";

            EditorUtility.SetDirty(this);
        }

        public bool IsAPIKeyValid()
        {
            return !string.IsNullOrEmpty(_apiKey) && _apiKey.Length > 10;
        }

        public Dictionary<string, object> GetTelemetryData()
        {
            if (!_enableTelemetry)
                return new Dictionary<string, object>();

            return new Dictionary<string, object>
            {
                { "total_requests", _usageStats.totalRequests },
                { "success_rate", _usageStats.GetSuccessRate() },
                { "selected_model", _selectedModel },
                { "average_response_time", _usageStats.averageResponseTime },
                { "prompt_history_count", _promptHistory.Count },
                { "auto_apply_enabled", _generationPreferences.autoApplyGenerated },
                { "live_preview_enabled", _generationPreferences.showLivePreview }
            };
        }

        public void ExportSettings(string filePath)
        {
            try
            {
                var exportData = new
                {
                    apiKey = "", // Don't export API key for security
                    selectedModel = _selectedModel,
                    requestTimeout = _requestTimeout,
                    maxRetries = _maxRetries,
                    generationPreferences = _generationPreferences,
                    editorPreferences = _editorPreferences,
                    enableDebugLogging = _enableDebugLogging,
                    enableTelemetry = _enableTelemetry,
                    customAPIEndpoint = _customAPIEndpoint
                };

                string json = JsonUtility.ToJson(exportData, true);
                File.WriteAllText(filePath, json);

                Debug.Log($"Settings exported to: {filePath}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to export settings: {ex.Message}");
                throw;
            }
        }

        public void ImportSettings(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"Settings file not found: {filePath}");
                }

                string json = File.ReadAllText(filePath);
                var importData = JsonUtility.FromJson<GeminiUIGeneratorSettings>(json);

                if (importData != null)
                {
                    // Import settings (except API key)
                    _selectedModel = importData._selectedModel;
                    _requestTimeout = importData._requestTimeout;
                    _maxRetries = importData._maxRetries;
                    _generationPreferences = importData._generationPreferences;
                    _editorPreferences = importData._editorPreferences;
                    _enableDebugLogging = importData._enableDebugLogging;
                    _enableTelemetry = importData._enableTelemetry;
                    _customAPIEndpoint = importData._customAPIEndpoint;

                    EditorUtility.SetDirty(this);
                    Debug.Log("Settings imported successfully");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to import settings: {ex.Message}");
                throw;
            }
        }

        private void OnValidate()
        {
            // Ensure values are within valid ranges
            _requestTimeout = Mathf.Clamp(_requestTimeout, 10f, 300f);
            _maxRetries = Mathf.Clamp(_maxRetries, 0, 10);

            if (_generationPreferences != null)
            {
                _generationPreferences.maxHistoryEntries = Mathf.Clamp(_generationPreferences.maxHistoryEntries, 1, 100);
            }

            if (_editorPreferences != null)
            {
                _editorPreferences.windowOpacity = Mathf.Clamp01(_editorPreferences.windowOpacity);
            }
        }
    }

    // Custom property drawer for the settings (optional)
    [CustomEditor(typeof(GeminiUIGeneratorSettings))]
    public class GeminiUIGeneratorSettingsEditor : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            var settings = (GeminiUIGeneratorSettings)target;

            EditorGUILayout.HelpBox("These are the settings for the Gemini UI Generator. " +
                                   "You can also access these through the generator window.", MessageType.Info);

            EditorGUILayout.Space();

            if (GUILayout.Button("Open Gemini UI Generator"))
            {
                GeminiUIGeneratorWindow.ShowWindow();
            }

            EditorGUILayout.Space();

            if (GUILayout.Button("Reset to Defaults"))
            {
                if (EditorUtility.DisplayDialog("Reset Settings",
                    "Are you sure you want to reset all settings to their default values?",
                    "Reset", "Cancel"))
                {
                    settings.ResetToDefaults();
                }
            }

            EditorGUILayout.Space();

            DrawDefaultInspector();
        }
    }
}
