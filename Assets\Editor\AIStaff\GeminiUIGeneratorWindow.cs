using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;
using UnityEditor.UIElements;
using System;
using System.Collections.Generic;

namespace AIStaff.UIGenerator
{
    public class GeminiUIGeneratorWindow : EditorWindow
    {
        [SerializeField] private VisualTreeAsset m_VisualTreeAsset;
        [SerializeField] private StyleSheet m_StyleSheet;

        // UI Elements
        private TextField promptField;
        private DropdownField modelDropdown;
        private TextField apiKeyField;
        private Button generateButton;
        private Button applyButton;
        private Button saveTemplateButton;
        private Button loadTemplateButton;
        private Button enhancePromptButton;
        private Button refreshModelsButton;
        private ScrollView codePreviewContainer;
        private Label statusLabel;
        private ProgressBar progressBar;
        private Toggle previewToggle;
        private DropdownField templateDropdown;

        // Core Components
        private GeminiAPIClient apiClient;
        private UICodeGenerator codeGenerator;
        private PromptTemplateManager templateManager;
        private UIPreviewRenderer previewRenderer;
        private GeminiUIGeneratorSettings settings;

        // State
        private string lastGeneratedUXML = "";
        private string lastGeneratedUSS = "";
        private bool isGenerating = false;
        private bool isEnhancingPrompt = false;
        private bool isRefreshingModels = false;
        private bool isCompactMode = false;
        private Vector2 lastWindowSize;

        [MenuItem("AI Staff/Gemini UI Generator")]
        public static void ShowWindow()
        {
            ShowWindowInternal();
        }

        public static GeminiUIGeneratorWindow ShowWindowInternal()
        {
            var window = GetWindow<GeminiUIGeneratorWindow>();
            window.titleContent = new GUIContent("Gemini UI Generator");
            window.minSize = new Vector2(400, 500);
            window.maxSize = new Vector2(1200, 1000);

            // Set initial size based on screen resolution
            var screenSize = new Vector2(Screen.currentResolution.width, Screen.currentResolution.height);
            var initialSize = new Vector2(
                Mathf.Clamp(screenSize.x * 0.6f, 600, 900),
                Mathf.Clamp(screenSize.y * 0.7f, 600, 800)
            );
            window.position = new Rect(
                (screenSize.x - initialSize.x) / 2,
                (screenSize.y - initialSize.y) / 2,
                initialSize.x,
                initialSize.y
            );

            return window;
        }

        public void CreateGUI()
        {
            // Load settings
            settings = GeminiUIGeneratorSettings.GetOrCreateSettings();

            // Initialize components
            apiClient = new GeminiAPIClient();
            codeGenerator = new UICodeGenerator();
            templateManager = new PromptTemplateManager();
            previewRenderer = new UIPreviewRenderer();

            // Load UXML and USS
            LoadUIAssets();

            // Setup UI
            SetupUI();

            // Bind events
            BindUIEvents();

            // Initialize state
            RefreshTemplateDropdown();
            UpdateUIState();

            // Setup adaptive layout
            lastWindowSize = position.size;
            UpdateLayoutMode();

            // Register for window resize events
            EditorApplication.update += CheckWindowResize;
        }

        private void LoadUIAssets()
        {
            // Load UXML
            string uxmlPath = "Assets/Editor/AIStaff/GeminiUIGeneratorWindow.uxml";
            m_VisualTreeAsset = AssetDatabase.LoadAssetAtPath<VisualTreeAsset>(uxmlPath);

            // Load USS
            string ussPath = "Assets/Editor/AIStaff/GeminiUIGeneratorWindow.uss";
            m_StyleSheet = AssetDatabase.LoadAssetAtPath<StyleSheet>(ussPath);

            if (m_VisualTreeAsset != null)
            {
                m_VisualTreeAsset.CloneTree(rootVisualElement);
            }
            else
            {
                CreateFallbackUI();
            }

            if (m_StyleSheet != null)
            {
                rootVisualElement.styleSheets.Add(m_StyleSheet);
            }
        }

        private void CreateFallbackUI()
        {
            // Create basic UI if UXML is not found
            var container = new VisualElement();
            container.style.paddingTop = new StyleLength(10);

            // Header
            var header = new Label("Gemini UI Generator");
            header.style.fontSize = 18;
            header.style.unityFontStyleAndWeight = FontStyle.Bold;
            header.style.marginBottom = 10;
            container.Add(header);

            // API Key field
            var apiKeyContainer = new VisualElement();
            apiKeyContainer.style.flexDirection = FlexDirection.Row;
            apiKeyContainer.style.marginBottom = 10;

            var apiKeyLabel = new Label("API Key:");
            apiKeyLabel.style.width = 80;
            apiKeyField = new TextField();
            apiKeyField.style.flexGrow = 1;
            apiKeyField.isPasswordField = true;

            apiKeyContainer.Add(apiKeyLabel);
            apiKeyContainer.Add(apiKeyField);
            container.Add(apiKeyContainer);

            // Model dropdown
            var modelContainer = new VisualElement();
            modelContainer.style.flexDirection = FlexDirection.Row;
            modelContainer.style.marginBottom = 10;

            var modelLabel = new Label("Model:");
            modelLabel.style.width = 80;
            modelDropdown = new DropdownField();
            modelDropdown.style.flexGrow = 1;

            refreshModelsButton = new Button(() => RefreshModels()) { text = "🔄" };
            refreshModelsButton.style.width = 30;
            refreshModelsButton.style.marginLeft = 5;

            modelContainer.Add(modelLabel);
            modelContainer.Add(modelDropdown);
            modelContainer.Add(refreshModelsButton);
            container.Add(modelContainer);

            // Prompt field
            var promptHeader = new VisualElement();
            promptHeader.style.flexDirection = FlexDirection.Row;
            promptHeader.style.justifyContent = Justify.SpaceBetween;
            promptHeader.style.alignItems = Align.Center;
            promptHeader.style.marginBottom = 5;

            var promptLabel = new Label("UI Description:");
            enhancePromptButton = new Button(() => EnhancePrompt()) { text = "✨ Enhance Prompt" };
            enhancePromptButton.style.height = 24;

            promptHeader.Add(promptLabel);
            promptHeader.Add(enhancePromptButton);
            container.Add(promptHeader);

            promptField = new TextField();
            promptField.multiline = true;
            promptField.style.height = 100;
            promptField.style.marginBottom = 10;
            container.Add(promptField);

            // Buttons
            var buttonContainer = new VisualElement();
            buttonContainer.style.flexDirection = FlexDirection.Row;
            buttonContainer.style.marginBottom = 10;

            generateButton = new Button(() => GenerateUI()) { text = "Generate UI" };
            generateButton.style.marginRight = 5;

            applyButton = new Button(() => ApplyGeneratedUI()) { text = "Apply to Scene" };
            applyButton.style.marginRight = 5;

            saveTemplateButton = new Button(() => SaveTemplate()) { text = "Save Template" };

            buttonContainer.Add(generateButton);
            buttonContainer.Add(applyButton);
            buttonContainer.Add(saveTemplateButton);
            container.Add(buttonContainer);

            // Status and progress
            statusLabel = new Label("Ready");
            statusLabel.style.marginBottom = 5;
            container.Add(statusLabel);

            progressBar = new ProgressBar();
            progressBar.style.marginBottom = 10;
            progressBar.style.display = DisplayStyle.None;
            container.Add(progressBar);

            // Code preview
            var previewLabel = new Label("Generated Code Preview:");
            previewLabel.style.marginBottom = 5;
            container.Add(previewLabel);

            codePreviewContainer = new ScrollView();
            codePreviewContainer.style.height = 200;
            codePreviewContainer.style.backgroundColor = new Color(0.2f, 0.2f, 0.2f);
            container.Add(codePreviewContainer);

            rootVisualElement.Add(container);
        }

        private void SetupUI()
        {
            // Get UI element references
            promptField = rootVisualElement.Q<TextField>("PromptField");
            modelDropdown = rootVisualElement.Q<DropdownField>("ModelDropdown");
            apiKeyField = rootVisualElement.Q<TextField>("ApiKeyField");
            generateButton = rootVisualElement.Q<Button>("GenerateButton");
            applyButton = rootVisualElement.Q<Button>("ApplyButton");
            saveTemplateButton = rootVisualElement.Q<Button>("SaveTemplateButton");
            loadTemplateButton = rootVisualElement.Q<Button>("LoadTemplateButton");
            enhancePromptButton = rootVisualElement.Q<Button>("EnhancePromptButton");
            refreshModelsButton = rootVisualElement.Q<Button>("RefreshModelsButton");
            codePreviewContainer = rootVisualElement.Q<ScrollView>("CodePreviewContainer");
            statusLabel = rootVisualElement.Q<Label>("StatusLabel");
            progressBar = rootVisualElement.Q<ProgressBar>("ProgressBar");
            previewToggle = rootVisualElement.Q<Toggle>("PreviewToggle");
            templateDropdown = rootVisualElement.Q<DropdownField>("TemplateDropdown");

            // Setup model dropdown
            if (modelDropdown != null)
            {
                modelDropdown.choices = apiClient.GetDefaultModels();
                modelDropdown.value = settings.selectedModel;
            }

            // Load saved values
            if (apiKeyField != null)
                apiKeyField.value = settings.apiKey;

            if (promptField != null)
                promptField.value = settings.lastPrompt;
        }

        private void BindUIEvents()
        {
            if (generateButton != null)
                generateButton.clicked += GenerateUI;

            if (applyButton != null)
                applyButton.clicked += ApplyGeneratedUI;

            if (saveTemplateButton != null)
                saveTemplateButton.clicked += SaveTemplate;

            if (loadTemplateButton != null)
                loadTemplateButton.clicked += LoadTemplate;

            if (enhancePromptButton != null)
                enhancePromptButton.clicked += EnhancePrompt;

            if (refreshModelsButton != null)
                refreshModelsButton.clicked += RefreshModels;

            if (apiKeyField != null)
                apiKeyField.RegisterValueChangedCallback(evt => {
                    settings.apiKey = evt.newValue;
                    EditorUtility.SetDirty(settings);
                });

            if (modelDropdown != null)
                modelDropdown.RegisterValueChangedCallback(evt => {
                    settings.selectedModel = evt.newValue;
                    EditorUtility.SetDirty(settings);
                });

            if (promptField != null)
                promptField.RegisterValueChangedCallback(evt => {
                    settings.lastPrompt = evt.newValue;
                    EditorUtility.SetDirty(settings);
                });

            if (templateDropdown != null)
                templateDropdown.RegisterValueChangedCallback(evt => {
                    LoadSelectedTemplate();
                });
        }

        private async void GenerateUI()
        {
            if (isGenerating) return;

            string prompt = promptField?.value ?? "";
            string apiKey = apiKeyField?.value ?? "";
            string model = modelDropdown?.value ?? "gemini-1.5-pro";

            if (string.IsNullOrEmpty(prompt))
            {
                ShowStatus("Please enter a UI description", true);
                return;
            }

            if (string.IsNullOrEmpty(apiKey))
            {
                ShowStatus("Please enter your Gemini API key", true);
                return;
            }

            try
            {
                isGenerating = true;
                UpdateUIState();
                ShowStatus("Generating UI with Gemini AI...");
                ShowProgress(true);

                // Generate UI code using Gemini API
                var result = await apiClient.GenerateUICode(prompt, apiKey, model);

                if (result.success)
                {
                    lastGeneratedUXML = result.uxmlCode;
                    lastGeneratedUSS = result.ussCode;

                    // Update preview
                    UpdateCodePreview();

                    ShowStatus("UI generated successfully!");
                }
                else
                {
                    ShowStatus($"Generation failed: {result.error}", true);
                }
            }
            catch (Exception ex)
            {
                ShowStatus($"Error: {ex.Message}", true);
                Debug.LogError($"Gemini UI Generator Error: {ex}");
            }
            finally
            {
                isGenerating = false;
                ShowProgress(false);
                UpdateUIState();
            }
        }

        private void ApplyGeneratedUI()
        {
            if (string.IsNullOrEmpty(lastGeneratedUXML))
            {
                ShowStatus("No generated UI to apply", true);
                return;
            }

            try
            {
                codeGenerator.ApplyGeneratedCode(lastGeneratedUXML, lastGeneratedUSS);
                ShowStatus("UI applied successfully!");
            }
            catch (Exception ex)
            {
                ShowStatus($"Failed to apply UI: {ex.Message}", true);
                Debug.LogError($"Apply UI Error: {ex}");
            }
        }

        private void SaveTemplate()
        {
            string prompt = promptField?.value ?? "";
            if (string.IsNullOrEmpty(prompt))
            {
                ShowStatus("No prompt to save", true);
                return;
            }

            string templateName = EditorUtility.SaveFilePanel(
                "Save Prompt Template",
                templateManager.GetTemplatesDirectory(),
                "template",
                "json"
            );

            if (!string.IsNullOrEmpty(templateName))
            {
                templateManager.SaveTemplate(templateName, prompt);
                RefreshTemplateDropdown();
                ShowStatus("Template saved successfully!");
            }
        }

        private void LoadTemplate()
        {
            string templatePath = EditorUtility.OpenFilePanel(
                "Load Prompt Template",
                templateManager.GetTemplatesDirectory(),
                "json"
            );

            if (!string.IsNullOrEmpty(templatePath))
            {
                string prompt = templateManager.LoadTemplate(templatePath);
                if (promptField != null)
                    promptField.value = prompt;

                RefreshTemplateDropdown();
                ShowStatus("Template loaded successfully!");
            }
        }

        private void LoadSelectedTemplate()
        {
            if (templateDropdown?.value != null && !string.IsNullOrEmpty(templateDropdown.value))
            {
                string prompt = templateManager.LoadTemplateByName(templateDropdown.value);
                if (promptField != null && !string.IsNullOrEmpty(prompt))
                {
                    promptField.value = prompt;
                    ShowStatus($"Loaded template: {templateDropdown.value}");
                }
            }
        }

        private async void EnhancePrompt()
        {
            if (isEnhancingPrompt) return;

            string prompt = promptField?.value ?? "";
            string apiKey = apiKeyField?.value ?? "";
            string model = modelDropdown?.value ?? "gemini-1.5-pro";

            if (string.IsNullOrEmpty(prompt))
            {
                ShowStatus("Please enter a prompt to enhance", true);
                return;
            }

            if (string.IsNullOrEmpty(apiKey))
            {
                ShowStatus("Please enter your Gemini API key", true);
                return;
            }

            try
            {
                isEnhancingPrompt = true;
                UpdateUIState();
                ShowStatus("Enhancing prompt with AI...");
                ShowProgress(true);

                // Enhance prompt using Gemini API
                var result = await apiClient.EnhancePrompt(prompt, apiKey, model);

                if (result.success)
                {
                    if (promptField != null)
                    {
                        promptField.value = result.uxmlCode; // Enhanced prompt is stored in uxmlCode field
                        ShowStatus("Prompt enhanced successfully!");
                    }
                }
                else
                {
                    ShowStatus($"Prompt enhancement failed: {result.error}", true);
                }
            }
            catch (Exception ex)
            {
                ShowStatus($"Error enhancing prompt: {ex.Message}", true);
                Debug.LogError($"Enhance Prompt Error: {ex}");
            }
            finally
            {
                isEnhancingPrompt = false;
                ShowProgress(false);
                UpdateUIState();
            }
        }

        private async void RefreshModels()
        {
            if (isRefreshingModels) return;

            string apiKey = apiKeyField?.value ?? "";

            if (string.IsNullOrEmpty(apiKey))
            {
                ShowStatus("Please enter your Gemini API key to refresh models", true);
                return;
            }

            try
            {
                isRefreshingModels = true;
                UpdateUIState();
                ShowStatus("Refreshing models from Google API...");
                ShowProgress(true);

                // Get models from API
                var models = await apiClient.GetAvailableModelsFromAPI(apiKey);

                if (modelDropdown != null)
                {
                    string currentSelection = modelDropdown.value;
                    modelDropdown.choices = models;

                    // Try to keep current selection if it's still available
                    if (models.Contains(currentSelection))
                    {
                        modelDropdown.value = currentSelection;
                    }
                    else if (models.Count > 0)
                    {
                        modelDropdown.value = models[0];
                        settings.selectedModel = models[0];
                        EditorUtility.SetDirty(settings);
                    }
                }

                ShowStatus($"Models refreshed successfully! Found {models.Count} models.");
            }
            catch (Exception ex)
            {
                ShowStatus($"Error refreshing models: {ex.Message}", true);
                Debug.LogError($"Refresh Models Error: {ex}");
            }
            finally
            {
                isRefreshingModels = false;
                ShowProgress(false);
                UpdateUIState();
            }
        }

        private void RefreshTemplateDropdown()
        {
            if (templateDropdown != null)
            {
                var templates = templateManager.GetAvailableTemplates();
                templateDropdown.choices = templates;
                if (templates.Count > 0)
                    templateDropdown.value = templates[0];
            }
        }

        private void UpdateCodePreview()
        {
            if (codePreviewContainer != null)
            {
                codePreviewContainer.Clear();

                if (!string.IsNullOrEmpty(lastGeneratedUXML) || !string.IsNullOrEmpty(lastGeneratedUSS))
                {
                    var preview = previewRenderer.CreatePreview(lastGeneratedUXML, lastGeneratedUSS);
                    codePreviewContainer.Add(preview);
                }
            }
        }

        private void UpdateUIState()
        {
            bool anyOperationInProgress = isGenerating || isEnhancingPrompt || isRefreshingModels;

            if (generateButton != null)
                generateButton.SetEnabled(!anyOperationInProgress);

            if (applyButton != null)
                applyButton.SetEnabled(!anyOperationInProgress && !string.IsNullOrEmpty(lastGeneratedUXML));

            if (enhancePromptButton != null)
                enhancePromptButton.SetEnabled(!anyOperationInProgress);

            if (refreshModelsButton != null)
                refreshModelsButton.SetEnabled(!anyOperationInProgress);

            if (saveTemplateButton != null)
                saveTemplateButton.SetEnabled(!anyOperationInProgress);

            if (loadTemplateButton != null)
                loadTemplateButton.SetEnabled(!anyOperationInProgress);
        }

        private void ShowStatus(string message, bool isError = false)
        {
            if (statusLabel != null)
            {
                statusLabel.text = message;
                statusLabel.style.color = isError ? Color.red : Color.white;
            }

            if (isError)
                Debug.LogError($"Gemini UI Generator: {message}");
            else
                Debug.Log($"Gemini UI Generator: {message}");
        }

        private void ShowProgress(bool show)
        {
            if (progressBar != null)
            {
                progressBar.style.display = show ? DisplayStyle.Flex : DisplayStyle.None;
                if (show)
                {
                    progressBar.value = 0;
                    // Start indeterminate progress animation
                    EditorApplication.update += UpdateProgress;
                }
                else
                {
                    EditorApplication.update -= UpdateProgress;
                }
            }
        }

        private void UpdateProgress()
        {
            if (progressBar != null && isGenerating)
            {
                progressBar.value = (progressBar.value + 0.01f) % 1.0f;
            }
        }

        private void CheckWindowResize()
        {
            if (position.size != lastWindowSize)
            {
                lastWindowSize = position.size;
                UpdateLayoutMode();
            }
        }

        private void UpdateLayoutMode()
        {
            bool shouldBeCompact = position.width < 600 || position.height < 650;

            if (shouldBeCompact != isCompactMode)
            {
                isCompactMode = shouldBeCompact;
                ApplyLayoutMode();
            }
        }

        private void ApplyLayoutMode()
        {
            if (rootVisualElement == null) return;

            if (isCompactMode)
            {
                rootVisualElement.AddToClassList("compact-mode");

                // Adjust specific elements for compact mode
                AdjustForCompactMode();
            }
            else
            {
                rootVisualElement.RemoveFromClassList("compact-mode");

                // Restore normal mode
                RestoreNormalMode();
            }
        }

        private void AdjustForCompactMode()
        {
            // Hide subtitle in compact mode
            var subtitle = rootVisualElement.Q<Label>("HeaderSubtitle");
            if (subtitle != null)
            {
                subtitle.style.display = DisplayStyle.None;
            }

            // Make prompt field smaller
            if (promptField != null)
            {
                promptField.style.minHeight = 60;
            }

            // Make code preview smaller
            if (codePreviewContainer != null)
            {
                codePreviewContainer.style.maxHeight = 150;
            }

            // Adjust button layout
            var actionSection = rootVisualElement.Q("ActionSection");
            if (actionSection != null)
            {
                actionSection.style.flexDirection = FlexDirection.Column;
            }
        }

        private void RestoreNormalMode()
        {
            // Show subtitle in normal mode
            var subtitle = rootVisualElement.Q<Label>("HeaderSubtitle");
            if (subtitle != null)
            {
                subtitle.style.display = DisplayStyle.Flex;
            }

            // Restore prompt field size
            if (promptField != null)
            {
                promptField.style.minHeight = 120;
            }

            // Restore code preview size
            if (codePreviewContainer != null)
            {
                codePreviewContainer.style.maxHeight = 400;
            }

            // Restore button layout
            var actionSection = rootVisualElement.Q("ActionSection");
            if (actionSection != null)
            {
                actionSection.style.flexDirection = FlexDirection.Row;
            }
        }

        private void OnDestroy()
        {
            EditorApplication.update -= UpdateProgress;
            EditorApplication.update -= CheckWindowResize;
        }
    }
}
